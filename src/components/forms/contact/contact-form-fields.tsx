import { type ReactElement, memo } from 'react';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

import { type ContactFormFieldsProps } from './contact-form-types';

/**
 * 必填字段组件
 */
export const RequiredFields = memo(
  ({ form, t }: ContactFormFieldsProps): ReactElement => {
    return (
      <>
        {/* 必填项：姓名 */}
        <FormField
          control={form.control}
          name='name'
          render={({ field, fieldState }) => (
            <FormItem>
              <FormLabel>
                {t('fields.name.label')}
                <span className='text-red-500' aria-label='必填字段'>
                  *
                </span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder={t('fields.name.placeholder')}
                  aria-describedby={
                    (fieldState as { error?: { message?: string } }).error !=
                    null
                      ? 'name-error'
                      : undefined
                  }
                  aria-invalid={Boolean(
                    (fieldState as { error?: { message?: string } }).error
                  )}
                  aria-required='true'
                  {...field}
                />
              </FormControl>
              <FormMessage id='name-error' role='alert' />
            </FormItem>
          )}
        />

        {/* 必填项：邮箱 */}
        <FormField
          control={form.control}
          name='email'
          render={({ field, fieldState }) => (
            <FormItem>
              <FormLabel>
                {t('fields.email.label')}
                <span className='text-red-500' aria-label='必填字段'>
                  *
                </span>
              </FormLabel>
              <FormControl>
                <Input
                  type='email'
                  placeholder={t('fields.email.placeholder')}
                  autoComplete='email'
                  aria-describedby={
                    (fieldState as { error?: { message?: string } }).error !=
                    null
                      ? 'email-error'
                      : undefined
                  }
                  aria-invalid={Boolean(
                    (fieldState as { error?: { message?: string } }).error
                  )}
                  aria-required='true'
                  {...field}
                />
              </FormControl>
              <FormMessage id='email-error' role='alert' />
            </FormItem>
          )}
        />
      </>
    );
  }
);

RequiredFields.displayName = 'RequiredFields';

/**
 * 可选字段组件
 */
export const OptionalFields = memo(
  ({ form, t }: ContactFormFieldsProps): ReactElement => {
    return (
      <>
        {/* 可选项：主题 */}
        <FormField
          control={form.control}
          name='subject'
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('fields.subject.label')}</FormLabel>
              <Select
                onValueChange={(value: string) => {
                  (field as { onChange: (value: string) => void }).onChange(
                    value
                  );
                }}
                defaultValue={(field as { value: string }).value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue
                      placeholder={t('fields.subject.placeholder')}
                    />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value='general'>
                    {t('fields.subject.options.general')}
                  </SelectItem>
                  <SelectItem value='support'>
                    {t('fields.subject.options.support')}
                  </SelectItem>
                  <SelectItem value='business'>
                    {t('fields.subject.options.business')}
                  </SelectItem>
                  <SelectItem value='other'>
                    {t('fields.subject.options.other')}
                  </SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 可选项：消息 */}
        <FormField
          control={form.control}
          name='message'
          render={({ field, fieldState }) => (
            <FormItem>
              <FormLabel>{t('fields.message.label')}</FormLabel>
              <FormControl>
                <Textarea
                  placeholder={t('fields.message.placeholder')}
                  className='min-h-[120px]'
                  aria-describedby={
                    (fieldState as { error?: { message?: string } }).error !=
                    null
                      ? 'message-error'
                      : undefined
                  }
                  aria-invalid={Boolean(
                    (fieldState as { error?: { message?: string } }).error
                  )}
                  {...field}
                />
              </FormControl>
              <FormMessage id='message-error' role='alert' />
            </FormItem>
          )}
        />
      </>
    );
  }
);

OptionalFields.displayName = 'OptionalFields';

/**
 * 联系表单字段组件
 */
export const ContactFormFields = memo(
  ({ form, t }: ContactFormFieldsProps): ReactElement => {
    return (
      <div className='space-y-4'>
        <RequiredFields form={form} t={t} />
        <OptionalFields form={form} t={t} />
      </div>
    );
  }
);

ContactFormFields.displayName = 'ContactFormFields';
