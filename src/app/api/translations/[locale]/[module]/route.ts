import { NextRequest, NextResponse } from 'next/server';

import { type TranslationModule } from '@/lib/i18n/dynamic-translation-loader';
import type { SupportedLocale } from '@/lib/i18n/language-detection';

/**
 * 动态翻译文件 API 路由
 * 功能特性：
 * - 按模块提供翻译文件
 * - 支持缓存控制和压缩
 * - 错误处理和回退机制
 * - 性能监控和日志记录
 */

// 支持的语言和模块
const SUPPORTED_LOCALES: SupportedLocale[] = ['en', 'zh'];

const SUPPORTED_MODULES: TranslationModule[] = [
  'common',
  'navigation',
  'home',
  'about',
  'contact',
  'blog',
  'products',
  'services',
  'forms',
  'errors',
];

// 翻译数据类型定义
interface TranslationData {
  [key: string]: string | TranslationData;
}

// 翻译文件映射（模拟分割后的翻译数据）
const TRANSLATION_MODULES: Record<
  SupportedLocale,
  Record<TranslationModule, TranslationData>
> = {
  en: {
    common: {
      title: 'Tucsenberg Web',
      description: 'Modern B2B Enterprise Website Template',
      loading: 'Loading...',
      error: 'An error occurred',
      retry: 'Retry',
      close: 'Close',
      save: 'Save',
      cancel: 'Cancel',
      submit: 'Submit',
      next: 'Next',
      previous: 'Previous',
    },
    navigation: {
      home: 'Home',
      products: 'Products',
      blog: 'Blog',
      about: 'About',
      services: 'Services',
      contact: 'Contact',
      language: 'Language',
      theme: 'Theme',
      menu: 'Menu',
      close: 'Close',
    },
    home: {
      hero: {
        title: 'Tucsenberg Web',
        subtitle: 'Modern B2B Enterprise Website Template',
        description: 'Built with Next.js 15, TypeScript, Tailwind CSS, and shadcn/ui.',
        cta: {
          primary: 'View Components',
          secondary: 'View Source Code',
        },
      },
      techStack: {
        title: 'Technology Stack',
        subtitle: 'Built with cutting-edge technologies',
      },
    },
    about: {
      title: 'About Us',
      subtitle: 'Learn more about our company',
      description: 'We create exceptional web experiences.',
    },
    contact: {
      title: 'Contact Us',
      subtitle: 'Get in touch with our team',
      form: {
        name: 'Name',
        email: 'Email',
        message: 'Message',
        submit: 'Send Message',
      },
    },
    blog: {
      title: 'Blog',
      subtitle: 'Latest news and insights',
      readMore: 'Read More',
      categories: 'Categories',
      tags: 'Tags',
    },
    products: {
      title: 'Products',
      subtitle: 'Discover our offerings',
      features: 'Features',
      pricing: 'Pricing',
      demo: 'View Demo',
    },
    services: {
      title: 'Services',
      subtitle: 'Professional services',
      consultation: 'Consultation',
      development: 'Development',
      support: 'Support',
    },
    forms: {
      validation: {
        required: 'This field is required',
        email: 'Please enter a valid email',
        minLength: 'Minimum {min} characters required',
        maxLength: 'Maximum {max} characters allowed',
      },
      messages: {
        success: 'Form submitted successfully',
        error: 'An error occurred',
        loading: 'Submitting...',
      },
    },
    errors: {
      404: {
        title: 'Page Not Found',
        description: 'The page does not exist.',
        action: 'Go Home',
      },
      500: {
        title: 'Internal Server Error',
        description: 'Something went wrong.',
        action: 'Try Again',
      },
      network: {
        title: 'Network Error',
        description: 'Check your connection.',
        action: 'Retry',
      },
    },
  },
  zh: {
    common: {
      title: 'Tucsenberg Web',
      description: '现代化 B2B 企业网站模板',
      loading: '加载中...',
      error: '发生错误',
      retry: '重试',
      close: '关闭',
      save: '保存',
      cancel: '取消',
      submit: '提交',
      next: '下一步',
      previous: '上一步',
    },
    navigation: {
      home: '首页',
      products: '产品',
      blog: '博客',
      about: '关于',
      services: '服务',
      contact: '联系',
      language: '语言',
      theme: '主题',
      menu: '菜单',
      close: '关闭',
    },
    home: {
      hero: {
        title: 'Tucsenberg Web',
        subtitle: '现代化 B2B 企业网站模板',
        description: '基于 Next.js 15、TypeScript、Tailwind CSS 构建。',
        cta: {
          primary: '查看组件',
          secondary: '查看源代码',
        },
      },
      techStack: {
        title: '技术栈',
        subtitle: '采用前沿技术构建',
      },
    },
    about: {
      title: '关于我们',
      subtitle: '了解我们的公司',
      description: '我们创造卓越的网络体验。',
    },
    contact: {
      title: '联系我们',
      subtitle: '与我们联系',
      form: {
        name: '姓名',
        email: '邮箱',
        message: '消息',
        submit: '发送消息',
      },
    },
    blog: {
      title: '博客',
      subtitle: '最新新闻和见解',
      readMore: '阅读更多',
      categories: '分类',
      tags: '标签',
    },
    products: {
      title: '产品',
      subtitle: '发现我们的产品',
      features: '功能',
      pricing: '价格',
      demo: '查看演示',
    },
    services: {
      title: '服务',
      subtitle: '专业服务',
      consultation: '咨询',
      development: '开发',
      support: '支持',
    },
    forms: {
      validation: {
        required: '此字段为必填项',
        email: '请输入有效邮箱',
        minLength: '最少需要 {min} 个字符',
        maxLength: '最多允许 {max} 个字符',
      },
      messages: {
        success: '表单提交成功',
        error: '提交时发生错误',
        loading: '提交中...',
      },
    },
    errors: {
      404: {
        title: '页面未找到',
        description: '页面不存在。',
        action: '返回首页',
      },
      500: {
        title: '服务器错误',
        description: '出了点问题。',
        action: '重试',
      },
      network: {
        title: '网络错误',
        description: '检查网络连接。',
        action: '重试',
      },
    },
  },
};

/**
 * 处理翻译文件请求
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ locale: string; module: string }> }
): Promise<NextResponse> {
  try {
    const resolvedParams = await params;
    const { locale, module } = resolvedParams;

    // 验证参数
    if (!SUPPORTED_LOCALES.includes(locale as SupportedLocale)) {
      return NextResponse.json(
        { error: 'Unsupported locale' },
        { status: 400 }
      );
    }

    if (!SUPPORTED_MODULES.includes(module as TranslationModule)) {
      return NextResponse.json(
        { error: 'Unsupported module' },
        { status: 400 }
      );
    }

    // 获取翻译数据
    const translationData = TRANSLATION_MODULES[locale as SupportedLocale]?.[module as TranslationModule];

    if (!translationData) {
      return NextResponse.json(
        { error: 'Translation not found' },
        { status: 404 }
      );
    }

    // 返回翻译数据
    return NextResponse.json(translationData, {
      headers: {
        'Cache-Control': 'public, max-age=3600, stale-while-revalidate=86400',
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Translation API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
