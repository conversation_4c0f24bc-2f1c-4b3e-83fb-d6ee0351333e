import React from 'react';



/* * 色盲滤镜 SVG 组件 * 功能特性： * - 红色盲滤镜 (Protanopia) * - 绿色盲滤镜 (Deuteranopia) * - 蓝色盲滤镜 (Tritanopia) * - 全色盲滤镜 (Achromatopsia) * - SVG 滤镜矩阵定义
 */
('use client');
/* * 色盲滤镜 SVG 组件 * 功能特性： * - 红色盲滤镜 (Protanopia) * - 绿色盲滤镜 (Deuteranopia) * - 蓝色盲滤镜 (Tritanopia) * - 全色盲滤镜 (Achromatopsia) * - SVG 滤镜矩阵定义
 */ ('use client');
/* * 色盲滤镜 SVG 组件 * 功能特性： * - 红色盲滤镜 (Protanopia) * - 绿色盲滤镜 (Deuteranopia) * - 蓝色盲滤镜 (Tritanopia) * - 全色盲滤镜 (Achromatopsia) * - SVG 滤镜矩阵定义
 */ /* * 色盲滤镜 SVG 组件 * 功能特性： * - 红色盲滤镜 (Protanopia) * - 绿色盲滤镜 (Deuteranopia) * - 蓝色盲滤镜 (Tritanopia) * - 全色盲滤镜 (Achromatopsia) * - SVG 滤镜矩阵定义
 */ /* * 色盲滤镜组件 * 提供 SVG 滤镜定义，用于模拟和补偿不同类型的色盲
 */

export function ColorBlindFilters(): React.ReactElement {
  return (
    <svg
      className='accessibility-colorblind-filters'
      aria-hidden='true'
      style={{ position: 'absolute', width: 0, height: 0, overflow: 'hidden' ;}}
    >
      {' '}
      <defs>
        {' '}
        <ColorBlindnessFilters /> <ContrastFilters /> <SimulationFilters />{' '}
        <EnhancementFilters />{' '}
      </defs>{' '}
    </svg>
  );
}
/* * 色盲类型滤镜组件
 */

function ColorBlindnessFilters(): React.ReactElement {
  return (
    <>
      {' '}
      {/* 红色盲滤镜 (Protanopia) */}{' '}
      <filter id='protanopia-filter'>
        {' '}
        <feColorMatrix
          type='matrix'
          values='0.567 0.433 0.000 0.000 0.000 0.558 0.442 0.000 0.000 0.000 0.000 0.242 0.758 0.000 0.000 0.000 0.000 0.000 1.000 0.000'
        />{' '}
      </filter>{' '}
      {/* 绿色盲滤镜 (Deuteranopia) */}{' '}
      <filter id='deuteranopia-filter'>
        {' '}
        <feColorMatrix
          type='matrix'
          values='0.625 0.375 0.000 0.000 0.000 0.700 0.300 0.000 0.000 0.000 0.000 0.300 0.700 0.000 0.000 0.000 0.000 0.000 1.000 0.000'
        />{' '}
      </filter>{' '}
      {/* 蓝色盲滤镜 (Tritanopia) */}{' '}
      <filter id='tritanopia-filter'>
        {' '}
        <feColorMatrix
          type='matrix'
          values='0.950 0.050 0.000 0.000 0.000 0.000 0.433 0.567 0.000 0.000 0.000 0.475 0.525 0.000 0.000 0.000 0.000 0.000 1.000 0.000'
        />{' '}
      </filter>{' '}
      {/* 全色盲滤镜 (Achromatopsia) */}{' '}
      <filter id='achromatopsia-filter'>
        {' '}
        <feColorMatrix
          type='matrix'
          values='0.299 0.587 0.114 0.000 0.000 0.299 0.587 0.114 0.000 0.000 0.299 0.587 0.114 0.000 0.000 0.000 0.000 0.000 1.000 0.000'
        />{' '}
      </filter>{' '}
    </>
  );
}
/* * 获取色盲类型对应的滤镜 ID
 */

export function getColorBlindFilterId(type: string): string {
  switch (type) {
    case 'protanopia':
      return 'protanopia-filter';
    case 'deuteranopia':
      return 'deuteranopia-filter';
    case 'tritanopia':
      return 'tritanopia-filter';
    case 'achromatopsia':
      return 'achromatopsia-filter';
    default:
      return '';
  }
}
/* * 获取模拟色盲滤镜 ID
 */

export function getSimulateColorBlindFilterId(type: string): string {
  switch (type) {
    case 'protanopia':
      return 'simulate-protanopia';
    case 'deuteranopia':
      return 'simulate-deuteranopia';
    case 'tritanopia':
      return 'simulate-tritanopia';
    default:
      return '';
  }
}
/* * 色盲类型信息
 */

export const colorBlindTypes = {
  protanopia: {
    name: '红色盲',
    description: '无法区分红色和绿色',
    prevalence: '约1%的男性',
    filterId: 'protanopia-filter',
  },
  deuteranopia: {
    name: '绿色盲',
    description: '无法区分红色和绿色',
    prevalence: '约1%的男性',
    filterId: 'deuteranopia-filter',
  },
  tritanopia: {
    name: '蓝色盲',
    description: '无法区分蓝色和黄色',
    prevalence: '约0.01%的人群',
    filterId: 'tritanopia-filter',
  },
  achromatopsia: {
    name: '全色盲',
    description: '只能看到灰度',
    prevalence: '约0.003%的人群',
    filterId: 'achromatopsia-filter',
  },
} as const;
/* * 对比度滤镜组件
 */

function ContrastFilters(): React.ReactElement {
  return (
    <>
      {' '}
      {/* 增强对比度滤镜 */}{' '}
      <filter id='enhanced-contrast-filter'>
        {' '}
        <feComponentTransfer>
          {' '}
          <feFuncA type='discrete' tableValues='0 .5 1' />{' '}
        </feComponentTransfer>{' '}
      </filter>{' '}
      {/* 高对比度滤镜 */}{' '}
      <filter id='high-contrast-filter'>
        {' '}
        <feColorMatrix
          type='matrix'
          values='1.5 0.0 0.0 0.0 -0.25 0.0 1.5 0.0 0.0 -0.25 0.0 0.0 1.5 0.0 -0.25 0.0 0.0 0.0 1.0 0.0'
        />{' '}
      </filter>{' '}
      {/* 色盲友好调色板滤镜 */}{' '}
      <filter id='colorblind-friendly-filter'>
        {' '}
        <feColorMatrix
          type='matrix'
          values='1.000 0.000 0.000 0.000 0.000 0.000 0.800 0.200 0.000 0.000 0.000 0.000 1.000 0.000 0.000 0.000 0.000 0.000 1.000 0.000'
        />{' '}
      </filter>{' '}
      {/* 红绿色盲通用滤镜 */}{' '}
      <filter id='red-green-colorblind-filter'>
        {' '}
        <feColorMatrix
          type='matrix'
          values='0.618 0.320 0.062 0.000 0.000 0.163 0.775 0.062 0.000 0.000 0.163 0.320 0.516 0.000 0.000 0.000 0.000 0.000 1.000 0.000'
        />{' '}
      </filter>{' '}
      {/* 蓝黄色盲滤镜 */}{' '}
      <filter id='blue-yellow-colorblind-filter'>
        {' '}
        <feColorMatrix
          type='matrix'
          values='1.000 0.000 0.000 0.000 0.000 0.000 1.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 1.000 0.000'
        />{' '}
      </filter>{' '}
    </>
  );
}
/* * 模拟滤镜组件
 */

function SimulationFilters(): React.ReactElement {
  return (
    <>
      {' '}
      {/* 模拟色盲滤镜组合 */}{' '}
      <filter id='simulate-protanopia'>
        {' '}
        <feColorMatrix
          type='matrix'
          values='0.152286 1.052583 -0.204868 0.000000 0.000000 0.114503 0.786281 0.099216 0.000000 0.000000 -0.003882 -0.048116 1.051998 0.000000 0.000000 0.000000 0.000000 0.000000 1.000000 0.000000'
        />{' '}
      </filter>{' '}
      <filter id='simulate-deuteranopia'>
        {' '}
        <feColorMatrix
          type='matrix'
          values='0.367322 0.860646 -0.227968 0.000000 0.000000 0.280085 0.672501 0.047413 0.000000 0.000000 -0.011820 0.042940 0.968881 0.000000 0.000000 0.000000 0.000000 0.000000 1.000000 0.000000'
        />{' '}
      </filter>{' '}
      <filter id='simulate-tritanopia'>
        {' '}
        <feColorMatrix
          type='matrix'
          values='1.255528 -0.076749 -0.178779 0.000000 0.000000 -0.078411 0.930809 0.147602 0.000000 0.000000 0.004733 0.691367 0.303900 0.000000 0.000000 0.000000 0.000000 0.000000 1.000000 0.000000'
        />{' '}
      </filter>{' '}
    </>
  );
}
/* * 增强滤镜组件
 */

function EnhancementFilters(): React.ReactElement {
  return (
    <>
      {' '}
      {/* 色彩增强滤镜 */}{' '}
      <filter id='color-enhancement-filter'>
        {' '}
        <feColorMatrix type='saturate' values='1.5' />{' '}
      </filter>{' '}
      {/* 亮度调整滤镜 */}{' '}
      <filter id='brightness-filter'>
        {' '}
        <feComponentTransfer>
          {' '}
          <feFuncR type='linear' slope='1.2' intercept='0.1' />{' '}
          <feFuncG type='linear' slope='1.2' intercept='0.1' />{' '}
          <feFuncB type='linear' slope='1.2' intercept='0.1' />{' '}
        </feComponentTransfer>{' '}
      </filter>{' '}
      {/* 对比度调整滤镜 */}{' '}
      <filter id='contrast-filter'>
        {' '}
        <feComponentTransfer>
          {' '}
          <feFuncR type='linear' slope='1.5' intercept='-0.25' />{' '}
          <feFuncG type='linear' slope='1.5' intercept='-0.25' />{' '}
          <feFuncB type='linear' slope='1.5' intercept='-0.25' />{' '}
        </feComponentTransfer>{' '}
      </filter>{' '}
      {/* 色温调整滤镜 */}{' '}
      <filter id='warm-filter'>
        {' '}
        <feColorMatrix
          type='matrix'
          values='1.1 0.0 0.0 0.0 0.0 0.0 1.0 0.0 0.0 0.0 0.0 0.0 0.8 0.0 0.0 0.0 0.0 0.0 1.0 0.0'
        />{' '}
      </filter>{' '}
      <filter id='cool-filter'>
        {' '}
        <feColorMatrix
          type='matrix'
          values='0.8 0.0 0.0 0.0 0.0 0.0 1.0 0.0 0.0 0.0 0.0 0.0 1.2 0.0 0.0 0.0 0.0 0.0 1.0 0.0'
        />{' '}
      </filter>{' '}
      {/* 夜间模式滤镜 */}{' '}
      <filter id='night-mode-filter'>
        {' '}
        <feColorMatrix
          type='matrix'
          values='0.3 0.3 0.0 0.0 0.0 0.0 0.6 0.0 0.0 0.0 0.0 0.0 0.1 0.0 0.0 0.0 0.0 0.0 1.0 0.0'
        />{' '}
      </filter>{' '}
      {/* 护眼模式滤镜 */}{' '}
      <filter id='eye-protection-filter'>
        {' '}
        <feColorMatrix
          type='matrix'
          values='1.0 0.0 0.0 0.0 0.0 0.0 0.95 0.0 0.0 0.0 0.0 0.0 0.8 0.0 0.0 0.0 0.0 0.0 1.0 0.0'
        />{' '}
      </filter>{' '}
    </>
  );
}
