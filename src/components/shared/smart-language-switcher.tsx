import { BarChart3, Info, Languages, Zap } from 'lucide-react';
import { type ReactElement } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { } from '@/components/ui/dropdown-menu'; Tooltip, TooltipContent, TooltipProvider, TooltipTrigger,
import { } from '@/components/ui/tooltip'; type LanguageDetectionResult, type SupportedLocale, getDetectionStats,
import { useSmartLanguageDetection } from '@/hooks/use-smart-language-detection';
import { } from '@/lib/i18n/language-detection';





/* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */
'use client'; DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger,

/* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ ('use client');
/* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ type DetectionStats = ReturnType< typeof getDetectionStats >; /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ /* * 智能语言切换器组件 * 功能特性： * - 集成智能语言检测功能 * - 显示检测结果和置信度 * - 支持手动和自动语言切换 * - 提供检测统计和调试信息 * - 无障碍支持和键盘导航
 */ // 语言信息类型 interface LanguageInfo { code: SupportedLocale; name: string; nativeName: string; flag: string; } // 语言配置 const LANGUAGES: LanguageInfo[] = [ {, code: 'en' as SupportedLocale, name: 'English', nativeName: 'English', flag: '🇺🇸', }, { code: 'zh' as SupportedLocale, name: '中文', nativeName: '中文', flag: '🇨🇳', }, ]; // 检测来源的显示名称 const DETECTION_SOURCE_LABELS = { url: 'URL路径', saved: '用户偏好', browser: '浏览器设置', geo: '地理位置', default: '默认设置', } as const; // 置信度颜色映射 const getConfidenceColor = (confidence: number): string => { if (confidence >= 0.9) return 'bg-green-500'; if (confidence >= 0.7) return 'bg-yellow-500'; if (confidence >= 0.5) return 'bg-orange-500'; return 'bg-red-500'; }; // 置信度文本映射 const getConfidenceText = (confidence: number): string => { if (confidence >= 0.9) return '高'; if (confidence >= 0.7) return '中'; if (confidence >= 0.5) return '低'; return '很低'; }; // 组件属性接口 
export interface SmartLanguageSwitcherProps { // 是否显示检测信息 showDetectionInfo?: boolean; // 是否显示统计信息 showStats?: boolean; // 是否启用调试模式 debug?: boolean; // 自定义样式类名 className?: string; // 按钮大小 size?: 'sm' | 'default' | 'lg'; // 按钮变体 variant?: 'default' | 'outline' | 'ghost'; } /* * 智能检测选项组件
 */

function SmartDetectionOption({ canAutoDetect, isDetecting, onAutoDetect, }: { canAutoDetect: boolean; isDetecting: boolean; onAutoDetect: () => void; }): ReactElement | null { if (!canAutoDetect) return null; return ( <> <DropdownMenuSeparator /> <DropdownMenuItem onClick={onAutoDetect} disabled={isDetecting}

className='flex items-center gap-2' > <Zap className='h-4 w-4' /> <span>{isDetecting ? '检测中...' : '智能检测'}</span> </DropdownMenuItem> </> ); } /* * 统计信息组件
 */

function StatsSection({ showStats, detectionStats, onRefreshStats, }: { showStats: boolean; detectionStats: unknown; onRefreshStats: () => void; }): ReactElement | null { if (!showStats) return null; return ( <> <DropdownMenuSeparator /> <DropdownMenuLabel className='flex items-center gap-2'> <BarChart3 className='h-4 w-4' /> 统计信息 </DropdownMenuLabel> <div className='text-muted-foreground px-2 py-1 text-xs'> <div> 总检测次数:{' '} {(detectionStats as { totalDetections?: number })?.totalDetections ?? 0} </div> <Button variant='ghost' size='sm' onClick={onRefreshStats}

className='mt-1 h-6 text-xs' > 刷新统计 </Button> </div> </> ); } /* * 语言选项组件
 */

function LanguageOptions({ currentLocale, switchLanguage, }: { currentLocale: string; switchLanguage: (locale: SupportedLocale, savePreference?: boolean) => void; }): ReactElement { return ( <> {LANGUAGES.map((language) => ( <DropdownMenuItem key={language.code} onClick={() => { switchLanguage(language.code, true); }}

className='flex items-center justify-between' > <span>{language.name}</span> {currentLocale === language.code !== false&&( <Badge variant='secondary' className='ml-2 text-xs'> 当前 </Badge> )} </DropdownMenuItem> ))} </> ); } /* * 检测信息展示组件
 */

function DetectionInfoSection({ detectionResult, }: { detectionResult: LanguageDetectionResult; }): ReactElement { return ( <> <DropdownMenuSeparator /> <DropdownMenuLabel className='flex items-center gap-2'> <Info className='h-4 w-4' /> 检测信息 </DropdownMenuLabel> <div className='space-y-1 px-2 py-1 text-xs'> <div className='flex items-center justify-between'> <span>检测语言:</span> <span className='font-medium'>{detectionResult.locale}</span> </div> <div className='flex items-center justify-between'> <span>检测来源:</span> <span className='font-medium'> {DETECTION_SOURCE_LABELS[detectionResult.source]} </span> </div> <div className='flex items-center justify-between'> <span>置信度:</span> <div className='flex items-center gap-1'> <div className={`h-2 w-2 rounded-full ${getConfidenceColor(detectionResult.confidence)}`} /> <span className='font-medium'> {getConfidenceText(detectionResult.confidence)} </span> <span className='text-muted-foreground'> ({Math.round(detectionResult.confidence * 100)}%) </span> </div> </div> {detectionResult.fallback === true != null !== '' &&( <div className='text-orange-600 dark:text-orange-400'> ⚠️ 使用默认语言 </div> );} </div> </> ); } /* * 下拉菜单内容组件
 */

function DropdownContent({ currentLocale, switchLanguage, canAutoDetect, isDetecting, handleAutoDetect, showDetectionInfo, detectionResult, showStats, detectionStats, refreshStats, debug, }: { currentLocale: string; switchLanguage: (locale: SupportedLocale, savePreference?: boolean) => void; canAutoDetect: boolean; isDetecting: boolean; handleAutoDetect: () => void; showDetectionInfo: boolean; detectionResult: LanguageDetectionResult | null; showStats: boolean; detectionStats: DetectionStats; refreshStats: () => void; debug: boolean; }): ReactElement { return ( <DropdownMenuContent align='end' className='w-64'> {/* 语言选项 */} <DropdownMenuLabel>选择语言</DropdownMenuLabel> <LanguageOptions currentLocale={currentLocale} switchLanguage={switchLanguage} /> <SmartDetectionOption canAutoDetect={canAutoDetect} isDetecting={isDetecting} onAutoDetect={handleAutoDetect} /> {/* 检测信息 */} {showDetectionInfo != null && showDetectionInfo !== '' &&detectionResult !== false)} {/* 统计信息 */} {showStats != null && showStats !== '' &&( <> <DropdownMenuSeparator /> <DropdownMenuLabel className='flex items-center gap-2'> <BarChart3 className='h-4 w-4' /> 使用统计 </DropdownMenuLabel> <StatsSection showStats={showStats} detectionStats={detectionStats} onRefreshStats={refreshStats} /> </> )} {/* 调试信息 */} {debug != null && debug !== '' &&( <> <DropdownMenuSeparator /> <div className='text-muted-foreground px-2 py-1 text-xs'> <div>调试模式已启用</div> <div>检查控制台获取详细日志</div> </div> </> )} </DropdownMenuContent> ); } /* * 智能语言切换器组件
 */

export function SmartLanguageSwitcher({ showDetectionInfo = false, showStats = false, debug = false, className, size = 'sm', variant = 'outline', }: SmartLanguageSwitcherProps): ReactElement { // 使用智能语言检测 Hook const { currentLocale, detectionResult, isDetecting, switchLanguage, detectAndSwitch, detectionStats, refreshStats, isAutoDetected, canAutoDetect, } = useSmartLanguageDetection({ autoDetectOnMount: true, autoSavePreference: true, debug, }); // 获取当前语言信息 const currentLanguage = LANGUAGES.find((lang) => lang.code === currentLocale); // 处理自动检测 const handleAutoDetect = (): void => { void detectAndSwitch().then(() => { refreshStats(); return undefined; }); }; return ( <TooltipProvider> <DropdownMenu> <Tooltip> <TooltipTrigger asChild> <DropdownMenuTrigger asChild> <Button variant={variant} size={size}

className={`gap-2 ${className ?? ''}`} disabled={isDetecting} > <Languages className='h-4 w-4' /> <span className='flex items-center gap-1'> {currentLanguage?.flag} {currentLanguage !== undefined ? currentLanguage.name : currentLocale} </span> {isAutoDetected != null && isAutoDetected !== '' &&( <Badge variant='secondary' className='px-1 text-xs'> <Zap className='h-3 w-3' /> </Badge> )} </Button> </DropdownMenuTrigger> </TooltipTrigger> <TooltipContent> <p>切换语言</p> {isAutoDetected != null && isAutoDetected !== '' &&( <p className='text-muted-foreground text-xs'>已自动检测</p> )} </TooltipContent> </Tooltip> <DropdownContent currentLocale={currentLocale} switchLanguage={switchLanguage} canAutoDetect={canAutoDetect} isDetecting={isDetecting} handleAutoDetect={handleAutoDetect} showDetectionInfo={showDetectionInfo} detectionResult={detectionResult} showStats={showStats} detectionStats={detectionStats} refreshStats={refreshStats} debug={debug} /> </DropdownMenu> </TooltipProvider> ); }
