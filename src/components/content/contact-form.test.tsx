import { beforeEach, describe, expect, it, vi } from 'vitest';

import { render, screen } from '@/test/utils';

import { ContactForm } from './contact-form'; // 定义类型 interface MockContactFormContainerProps { memoizedT: (key: string) => string; } // 模拟 next-intl const mockUseTranslations = vi.fn(); vi.mock('next-intl', () => ({ useTranslations: (namespace: string) => { mockUseTranslations(namespace); return mockUseTranslations() as (key: string) => string; }, })); // 模拟 ContactFormContainer 组件 const mockContactFormContainer = vi.fn(); vi.mock('@/components/forms/contact/contact-form-container', () => ({ ContactFormContainer: (props: MockContactFormContainerProps) => { mockContactFormContainer(props); return ( <div data-testid='contact-form-container'> Mocked ContactFormContainer </div> ); }, })); // 常量定义 const CONTACT_FORM_CONTAINER_TEST_ID = 'contact-form-container';






const TRANSLATION_NAMESPACE = 'pages.contact.form'; describe('ContactForm 组件', () => { beforeEach(() => { vi.clearAllMocks(); // 设置模拟翻译函数 const mockT = vi.fn((key: string) => { const translations = new Map([ ['title', '联系我们'], ['subtitle', '我们很乐意听到您的声音'], ['fields.name.label', '姓名'], ['fields.email.label', '邮箱'], ['submit', '提交'], ['success', '提交成功！'], ]); return translations.get(key) ?? key; }); mockUseTranslations.mockImplementation((_namespace: string) => { return mockT; }); }); it('应该正确渲染联系表单', () => { render(<ContactForm />); expect( screen.getByTestId(CONTACT_FORM_CONTAINER_TEST_ID) ).toBeInTheDocument(); }); it('应该调用 useTranslations 获取翻译函数', () => { render(<ContactForm />); expect(mockUseTranslations).toHaveBeenCalledWith(TRANSLATION_NAMESPACE); }); it('应该将 memoized 翻译函数传递给 ContactFormContainer', () => { render(<ContactForm />); expect(mockContactFormContainer).toHaveBeenCalledTimes(1);

const passedProps = mockContactFormContainer.mock .calls[0]?.[0] as MockContactFormContainerProps; expect(passedProps).toHaveProperty('memoizedT'); expect(typeof passedProps?.memoizedT).toBe('function'); }); it('应该正确缓存翻译函数（useMemo 测试）', () => { const { rerender } = render(<ContactForm />);

const firstCall = mockContactFormContainer.mock .calls[0]?.[0] as MockContactFormContainerProps;

const firstMemoizedT = firstCall?.memoizedT; // 重新渲染组件 rerender(<ContactForm />);

const secondCall = mockContactFormContainer.mock .calls[1]?.[0] as MockContactFormContainerProps;

const secondMemoizedT = secondCall?.memoizedT; // 由于 useMemo 的依赖没有变化，翻译函数应该是同一个引用 expect(firstMemoizedT).toBe(secondMemoizedT); }); it('传递的翻译函数应该能正确工作', () => { render(<ContactForm />);

const passedProps = mockContactFormContainer.mock .calls[0]?.[0] as MockContactFormContainerProps;

const memoizedT = passedProps?.memoizedT; // 测试翻译函数是否正确工作 expect(memoizedT?.('title')).toBe('联系我们'); expect(memoizedT?.('fields.name.label')).toBe('姓名'); expect(memoizedT?.('submit')).toBe('提交'); }); it('应该处理未知的翻译键', () => { render(<ContactForm />);

const passedProps = mockContactFormContainer.mock .calls[0]?.[0] as MockContactFormContainerProps;

const memoizedT = passedProps?.memoizedT; // 测试未知键的处理 expect(memoizedT?.('unknown.key')).toBe('unknown.key'); }); it('应该是客户端组件（use client 指令）', () => { // 这个测试验证组件文件包含 'use client' 指令 // 在实际应用中，这通过文件内容验证 expect(true).toBe(true); // 占位测试，实际应该检查文件内容 }); it('应该有正确的组件注释和文档', () => { // 验证组件具有适当的 JSDoc 注释 // 这个测试主要是为了确保代码质量 render(<ContactForm />); // 验证组件正常渲染，间接验证了代码结构正确 expect( screen.getByTestId(CONTACT_FORM_CONTAINER_TEST_ID) ).toBeInTheDocument(); }); it('应该正确处理翻译函数的变化', () => { render(<ContactForm />); // 验证组件能够正常渲染，间接验证翻译函数处理正确 expect( screen.getByTestId(CONTACT_FORM_CONTAINER_TEST_ID) ).toBeInTheDocument(); // 验证mock函数被调用 expect(mockContactFormContainer).toHaveBeenCalled(); expect(mockUseTranslations).toHaveBeenCalledWith(TRANSLATION_NAMESPACE); }); it('应该保持组件的简洁性', () => { // 验证主组件保持简洁，只负责翻译和容器渲染 render(<ContactForm />); // 验证只渲染了一个容器组件 expect(screen.getAllByTestId(CONTACT_FORM_CONTAINER_TEST_ID)).toHaveLength( 1 ); // 验证没有额外的 DOM 元素 const container = screen.getByTestId( CONTACT_FORM_CONTAINER_TEST_ID ).parentElement; expect(container?.children).toHaveLength(1); }); });
