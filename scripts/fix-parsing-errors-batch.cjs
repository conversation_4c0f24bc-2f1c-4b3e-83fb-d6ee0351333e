#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * 批量修复解析错误的脚本
 */

function fixParsingErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    const originalContent = content;
    
    // 1. 修复常见的格式化问题
    const formatPatterns = [
      // 修复单行挤压的函数声明
      {
        pattern: /(\w+)\s*:\s*(\w+)\s*=>\s*{([^}]+)}\s*;/g,
        replacement: (match, name, type, body) => {
          return `${name}: ${type} => {\n  ${body.trim()}\n};`;
        }
      },
      
      // 修复单行挤压的对象声明
      {
        pattern: /const\s+(\w+)\s*=\s*{([^}]+)}\s*;/g,
        replacement: (match, name, body) => {
          const items = body.split(',').map(item => item.trim()).filter(item => item);
          if (items.length > 2) {
            return `const ${name} = {\n  ${items.join(',\n  ')}\n};`;
          }
          return match;
        }
      },
      
      // 修复单行挤压的接口声明
      {
        pattern: /interface\s+(\w+)\s*{([^}]+)}/g,
        replacement: (match, name, body) => {
          const items = body.split(';').map(item => item.trim()).filter(item => item);
          if (items.length > 1) {
            return `interface ${name} {\n  ${items.join(';\n  ')};\n}`;
          }
          return match;
        }
      },
      
      // 修复缺失的分号
      {
        pattern: /(\w+:\s*[^;,\n}]+)(\s*[}\n])/g,
        replacement: '$1;$2'
      },
      
      // 修复缺失的逗号
      {
        pattern: /(\w+:\s*[^,;\n}]+)(\s+\w+:)/g,
        replacement: '$1,$2'
      }
    ];
    
    // 2. 修复导入语句问题
    const importPatterns = [
      // 修复缺失的 from 关键字
      {
        pattern: /import\s+{([^}]+)}\s+['"]([^'"]+)['"];/g,
        replacement: "import { $1 } from '$2';"
      },
      
      // 修复缺失的引号
      {
        pattern: /import\s+{([^}]+)}\s+from\s+([^'"][^;\n]+);/g,
        replacement: "import { $1 } from '$2';"
      }
    ];
    
    // 3. 修复类型声明问题
    const typePatterns = [
      // 修复缺失的冒号
      {
        pattern: /(\w+)\s+(\w+)\s*=/g,
        replacement: '$1: $2 ='
      },
      
      // 修复缺失的等号
      {
        pattern: /const\s+(\w+):\s*([^=\n]+)\s+([^=\n]+)/g,
        replacement: 'const $1: $2 = $3'
      }
    ];
    
    // 应用所有修复模式
    const allPatterns = [
      ...importPatterns,
      ...typePatterns,
      ...formatPatterns
    ];
    
    for (const { pattern, replacement } of allPatterns) {
      if (typeof replacement === 'function') {
        const newContent = content.replace(pattern, replacement);
        if (newContent !== content) {
          content = newContent;
          modified = true;
        }
      } else {
        const newContent = content.replace(pattern, replacement);
        if (newContent !== content) {
          content = newContent;
          modified = true;
        }
      }
    }
    
    // 4. 特殊处理：修复严重格式化问题
    const lines = content.split('\n');
    const fixedLines = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmed = line.trim();
      
      // 检查是否有多个语句在一行
      if (trimmed.includes(';') && trimmed.includes('{') && trimmed.includes('}')) {
        // 尝试分解单行多语句
        const parts = trimmed.split(';');
        for (let j = 0; j < parts.length; j++) {
          const part = parts[j].trim();
          if (part) {
            if (j === parts.length - 1 && !part.endsWith(';')) {
              fixedLines.push(part);
            } else {
              fixedLines.push(part + ';');
            }
          }
        }
        modified = true;
      } else {
        fixedLines.push(line);
      }
    }
    
    if (modified) {
      content = fixedLines.join('\n');
    }
    
    // 5. 最后清理多余的空行
    content = content.replace(/\n{3,}/g, '\n\n');
    
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 修复解析错误: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🚀 开始批量修复解析错误...\n');
  
  // 获取有解析错误的文件
  let errorFiles = [];
  try {
    const lintOutput = execSync('pnpm lint 2>&1 | grep -B 1 "Parsing error"', { encoding: 'utf8' });
    const lines = lintOutput.split('\n').filter(line => line.trim());
    
    for (const line of lines) {
      if (line.includes('.ts') || line.includes('.tsx')) {
        const match = line.match(/^([^:]+):/);
        if (match) {
          const filePath = match[1].trim();
          if (filePath.startsWith('./')) {
            errorFiles.push(filePath.substring(2));
          } else {
            errorFiles.push(filePath);
          }
        }
      }
    }
  } catch (error) {
    console.log('无法获取解析错误文件列表，将处理所有文件');
  }
  
  // 去重
  errorFiles = [...new Set(errorFiles)];
  
  if (errorFiles.length === 0) {
    console.log('未找到有解析错误的文件，处理所有 TypeScript 文件');
    // 如果没有找到特定文件，处理所有文件
    const extensions = ['.ts', '.tsx'];
    const directories = ['src/lib'];
    
    for (const dir of directories) {
      if (!fs.existsSync(dir)) continue;
      
      function processDirectory(dirPath) {
        const items = fs.readdirSync(dirPath);
        
        for (const item of items) {
          const fullPath = path.join(dirPath, item);
          const stat = fs.statSync(fullPath);
          
          if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
            processDirectory(fullPath);
          } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
            errorFiles.push(fullPath);
          }
        }
      }
      
      processDirectory(dir);
    }
  }
  
  let totalFixed = 0;
  
  for (const filePath of errorFiles) {
    if (fs.existsSync(filePath) && fixParsingErrors(filePath)) {
      totalFixed++;
    }
  }
  
  console.log(`\n✅ 完成！共修复了 ${totalFixed} 个文件的解析错误`);
  
  // 运行 ESLint 检查结果
  console.log('\n🔍 检查修复结果...');
  try {
    const result = execSync('pnpm lint 2>&1 | grep "Error:" | wc -l', { encoding: 'utf8' });
    console.log(`剩余错误数量: ${result.trim()}`);
  } catch (error) {
    console.log('检查完成');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixParsingErrors };
