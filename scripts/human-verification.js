#!/usr/bin/env node

/**
 * 人类确认流程脚本 - 第三层验证
 * 生成标准化确认清单和验证机制
 * 基于AI辅助质量体系文档的确认项编写原则
 */
import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import {
  generateChecklistByTaskType,
  validateChecklist,
} from './verification-templates.js';

const execAsync = promisify(exec);

// 加载配置
const configPath = path.join(process.cwd(), '.verification-config.json');
let config = {};

if (fs.existsSync(configPath)) {
  config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
} else {
  console.warn('⚠️ 人类确认配置文件不存在，使用默认配置');
  config = {
    humanVerification: { enabled: true, timeoutMinutes: 10 },
    logging: { directory: '.verification-log' },
  };
}

/**
 * 生成唯一的验证令牌
 */
function generateVerificationToken(taskId) {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 15);
  return `verify_${taskId}_${timestamp}_${random}`;
}

/**
 * 创建验证日志记录
 */
function createVerificationLog(taskId, checklist, token) {
  const logDir = config.logging?.directory || '.verification-log';

  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }

  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const logFile = path.join(logDir, `verification-${taskId}-${timestamp}.json`);

  const logData = {
    taskId,
    token,
    checklist,
    startTime: new Date().toISOString(),
    status: 'started',
    userAgent: process.env.USER || 'unknown',
    version: '1.0.0',
  };

  fs.writeFileSync(logFile, JSON.stringify(logData, null, 2));
  return logFile;
}

/**
 * 更新验证日志状态
 */
function updateVerificationLog(logFile, updates) {
  if (fs.existsSync(logFile)) {
    const logData = JSON.parse(fs.readFileSync(logFile, 'utf8'));
    Object.assign(logData, updates);
    logData.lastUpdated = new Date().toISOString();
    fs.writeFileSync(logFile, JSON.stringify(logData, null, 2));
  }
}

/**
 * 显示确认清单
 */
function displayChecklist(checklist) {
  console.log('\n📋 人类确认清单');
  console.log('='.repeat(60));
  console.log(`📝 任务: ${checklist.taskName}`);
  console.log(`⏱️ 预估时间: ${checklist.estimatedTime}`);
  console.log(`🔢 确认项数量: ${checklist.items.length} 个`);
  console.log('');

  checklist.items.forEach((item, index) => {
    const required = item.required ? '🔴 必需' : '🟡 可选';
    console.log(`${index + 1}. ${required} ${item.description}`);
    console.log(`   📖 操作指导: ${item.instruction}`);

    if (item.command) {
      console.log(`   💻 执行命令: ${item.command}`);
    }
    if (item.url) {
      console.log(`   🌐 访问地址: ${item.url}`);
    }
    if (item.filePath) {
      console.log(`   📁 检查文件: ${item.filePath}`);
    }
    console.log('');
  });
}

/**
 * 等待用户确认
 */
async function waitForUserConfirmation(checklist) {
  console.log('🤔 请按照上述清单逐项确认，完成后输入确认信息：');
  console.log('');

  const readline = await import('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  const askQuestion = (question) => {
    return new Promise((resolve) => {
      rl.question(question, resolve);
    });
  };

  const confirmations = {};

  for (let i = 0; i < checklist.items.length; i++) {
    const item = checklist.items[i];
    const question = `✅ 确认项 ${i + 1} (${item.description}) 已完成? (y/n/s=跳过): `;

    let answer;
    do {
      answer = await askQuestion(question);
      answer = answer.toLowerCase().trim();
    } while (!['y', 'yes', 'n', 'no', 's', 'skip'].includes(answer));

    if (['y', 'yes'].includes(answer)) {
      confirmations[item.id] = {
        completed: true,
        timestamp: new Date().toISOString(),
      };
    } else if (['s', 'skip'].includes(answer)) {
      confirmations[item.id] = {
        completed: false,
        skipped: true,
        timestamp: new Date().toISOString(),
      };
    } else {
      confirmations[item.id] = {
        completed: false,
        timestamp: new Date().toISOString(),
      };
    }
  }

  rl.close();
  return confirmations;
}

/**
 * 验证确认结果
 */
function validateConfirmations(checklist, confirmations) {
  const validation = {
    isValid: true,
    completedRequired: 0,
    totalRequired: 0,
    completedOptional: 0,
    totalOptional: 0,
    failedItems: [],
  };

  checklist.items.forEach((item) => {
    const confirmation = confirmations[item.id];

    if (item.required) {
      validation.totalRequired++;
      if (confirmation?.completed) {
        validation.completedRequired++;
      } else if (!confirmation?.skipped) {
        validation.failedItems.push(item.description);
      }
    } else {
      validation.totalOptional++;
      if (confirmation?.completed) {
        validation.completedOptional++;
      }
    }
  });

  // 检查是否所有必需项都已完成
  if (
    validation.completedRequired < validation.totalRequired &&
    validation.failedItems.length > 0
  ) {
    validation.isValid = false;
  }

  return validation;
}

/**
 * 打开浏览器进行验证提醒
 */
async function openBrowserReminder() {
  if (config.browserIntegration?.autoOpen) {
    const url = config.browserIntegration.defaultUrl || 'http://localhost:3000';
    console.log(`🌐 正在打开浏览器访问 ${url} 进行验证...`);

    try {
      const platform = process.platform;
      let command;

      if (platform === 'darwin') {
        command = `open ${url}`;
      } else if (platform === 'win32') {
        command = `start ${url}`;
      } else {
        command = `xdg-open ${url}`;
      }

      await execAsync(command);
    } catch {
      console.warn('⚠️ 无法自动打开浏览器，请手动访问:', url);
    }
  }
}

/**
 * 主执行函数
 */
async function main() {
  console.log('👤 开始第三层人类确认验证...');
  console.log('📋 基于AI辅助质量体系文档标准');

  const startTime = Date.now();
  const taskId = process.argv[2] || 'demo-task';

  // 模拟任务数据（实际使用中会从任务系统获取）
  const mockTaskData = {
    id: taskId,
    name: '示例任务 - 人类确认测试',
    relatedFiles: [
      { path: 'scripts/human-verification.js', type: 'CREATE' },
      { path: '.verification-config.json', type: 'CREATE' },
    ],
  };

  try {
    // 生成确认清单
    console.log('📝 生成标准化确认清单...');
    const checklist = generateChecklistByTaskType(mockTaskData);

    // 验证清单
    const validation = validateChecklist(checklist);
    if (!validation.isValid) {
      throw new Error(`确认清单验证失败: ${validation.errors.join(', ')}`);
    }

    // 生成验证令牌
    const token = generateVerificationToken(taskId);

    // 创建日志记录
    const logFile = createVerificationLog(taskId, checklist, token);
    console.log(`📄 验证日志已创建: ${logFile}`);

    // 打开浏览器提醒
    await openBrowserReminder();

    // 显示确认清单
    displayChecklist(checklist);

    // 等待用户确认
    const confirmations = await waitForUserConfirmation(checklist);

    // 验证确认结果
    const confirmationValidation = validateConfirmations(
      checklist,
      confirmations
    );

    // 更新日志
    updateVerificationLog(logFile, {
      confirmations,
      validation: confirmationValidation,
      token,
      endTime: new Date().toISOString(),
      status: confirmationValidation.isValid ? 'completed' : 'failed',
    });

    // 显示结果
    const totalTime = Date.now() - startTime;
    console.log('\n📊 人类确认结果摘要');
    console.log('='.repeat(50));
    console.log(
      `✅ 必需项完成: ${confirmationValidation.completedRequired}/${confirmationValidation.totalRequired}`
    );
    console.log(
      `🟡 可选项完成: ${confirmationValidation.completedOptional}/${confirmationValidation.totalOptional}`
    );
    console.log(`🔑 验证令牌: ${token}`);
    console.log(`⏱️ 确认耗时: ${Math.round(totalTime / 1000)}秒`);

    if (confirmationValidation.isValid) {
      console.log('🎉 人类确认验证通过！');
      return {
        success: true,
        token,
        validation: confirmationValidation,
        logFile,
        totalTime,
      };
    } else {
      console.log('❌ 人类确认验证失败');
      console.log('未完成的必需项:');
      confirmationValidation.failedItems.forEach((item) => {
        console.log(`  - ${item}`);
      });
      return {
        success: false,
        validation: confirmationValidation,
        logFile,
        totalTime,
      };
    }
  } catch (error) {
    console.error('❌ 人类确认执行失败:', error.message);
    return {
      success: false,
      error: error.message,
    };
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error('💥 人类确认脚本执行失败:', error);
    process.exit(1);
  });
}

export {
  main,
  generateVerificationToken,
  createVerificationLog,
  displayChecklist,
};
