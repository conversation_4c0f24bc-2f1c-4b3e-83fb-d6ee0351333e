#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * 修复特定错误类型的脚本
 */

function fixSpecificErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    const originalContent = content;
    
    // 1. 修复 "const:" 错误
    content = content.replace(/const:\s*(\w+)/g, 'const $1');
    if (content !== originalContent) modified = true;
    
    // 2. 修复 "typeof:" 错误
    content = content.replace(/typeof:\s*(\w+)/g, 'typeof $1');
    if (content !== originalContent) modified = true;
    
    // 3. 修复缺失的分号
    content = content.replace(/(\w+:\s*[^;,\n}]+)(\s*[}\n])/g, '$1;$2');
    if (content !== originalContent) modified = true;
    
    // 4. 修复缺失的逗号
    content = content.replace(/(\w+:\s*[^,;\n}]+)(\s+\w+:)/g, '$1,$2');
    if (content !== originalContent) modified = true;
    
    // 5. 修复缺失的等号
    content = content.replace(/const\s+(\w+):\s*([^=\n]+)\s+([^=\n]+)/g, 'const $1: $2 = $3');
    if (content !== originalContent) modified = true;
    
    // 6. 修复 export const: 错误
    content = content.replace(/export\s+const:\s*(\w+)/g, 'export const $1');
    if (content !== originalContent) modified = true;
    
    // 7. 修复函数参数中的错误
    content = content.replace(/\(\s*,\s*(\w+)/g, '($1');
    if (content !== originalContent) modified = true;
    
    // 8. 修复对象属性中的错误
    content = content.replace(/{\s*;/g, '{');
    if (content !== originalContent) modified = true;
    
    // 9. 修复多余的分号
    content = content.replace(/;{2,}/g, ';');
    if (content !== originalContent) modified = true;
    
    // 10. 修复严格布尔表达式
    content = content.replace(/if\s*\(\s*(\w+)\s*\)/g, 'if ($1 !== null && $1 !== undefined)');
    if (content !== originalContent) modified = true;
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 修复特定错误: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🚀 开始修复特定错误类型...\n');
  
  // 处理所有 TypeScript 文件
  const extensions = ['.ts', '.tsx'];
  const directories = ['src/lib', 'src/components'];
  
  let totalFixed = 0;
  
  for (const dir of directories) {
    if (!fs.existsSync(dir)) continue;
    
    function processDirectory(dirPath) {
      const items = fs.readdirSync(dirPath);
      
      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          processDirectory(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          if (fixSpecificErrors(fullPath)) {
            totalFixed++;
          }
        }
      }
    }
    
    processDirectory(dir);
  }
  
  console.log(`\n✅ 完成！共修复了 ${totalFixed} 个文件的特定错误`);
  
  // 运行 ESLint 检查结果
  console.log('\n🔍 检查修复结果...');
  try {
    const result = execSync('pnpm lint 2>&1 | grep "Error:" | wc -l', { encoding: 'utf8' });
    console.log(`剩余错误数量: ${result.trim()}`);
  } catch (error) {
    console.log('检查完成');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixSpecificErrors };
