#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * 修复导入问题的脚本
 */

function fixImports(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 1. 修复重复导入
    const lines = content.split('\n');
    const importLines = [];
    const nonImportLines = [];
    const seenImports = new Set();

    for (const line of lines) {
      if (line.trim().startsWith('import ')) {
        // 提取导入的模块名
        const moduleMatch = line.match(/from\s+['"]([^'"]+)['"]/);
        if (moduleMatch) {
          const moduleName = moduleMatch[1];

          // 检查是否已经导入过这个模块
          if (!seenImports.has(moduleName)) {
            seenImports.add(moduleName);
            importLines.push(line);
          } else {
            // 合并重复的导入
            const existingIndex = importLines.findIndex(
              (existingLine) =>
                existingLine.includes(`from '${moduleName}'`) ||
                existingLine.includes(`from "${moduleName}"`)
            );

            if (existingIndex !== -1) {
              // 提取当前行的导入内容
              const currentImports = line.match(/import\s+{([^}]+)}/);
              const existingImports =
                importLines[existingIndex].match(/import\s+{([^}]+)}/);

              if (currentImports && existingImports) {
                // 合并导入项
                const currentItems = currentImports[1]
                  .split(',')
                  .map((s) => s.trim());
                const existingItems = existingImports[1]
                  .split(',')
                  .map((s) => s.trim());
                const allItems = [
                  ...new Set([...existingItems, ...currentItems]),
                ];

                // 更新导入行
                importLines[existingIndex] = importLines[existingIndex].replace(
                  /import\s+{[^}]+}/,
                  `import { ${allItems.join(', ')} }`
                );
                modified = true;
              }
            }
          }
        } else {
          importLines.push(line);
        }
      } else {
        nonImportLines.push(line);
      }
    }

    // 2. 排序导入
    const sortedImports = importLines.sort((a, b) => {
      // 提取模块名进行排序
      const getModuleName = (line) => {
        const match = line.match(/from\s+['"]([^'"]+)['"]/);
        return match ? match[1] : '';
      };

      const moduleA = getModuleName(a);
      const moduleB = getModuleName(b);

      // 内置模块优先
      const isBuiltinA = !moduleA.startsWith('.') && !moduleA.startsWith('@/');
      const isBuiltinB = !moduleB.startsWith('.') && !moduleB.startsWith('@/');

      if (isBuiltinA && !isBuiltinB) return -1;
      if (!isBuiltinA && isBuiltinB) return 1;

      // 相对路径最后
      const isRelativeA = moduleA.startsWith('.');
      const isRelativeB = moduleB.startsWith('.');

      if (!isRelativeA && isRelativeB) return -1;
      if (isRelativeA && !isRelativeB) return 1;

      // 字母顺序
      return moduleA.localeCompare(moduleB);
    });

    // 3. 添加导入组之间的空行
    const groupedImports = [];
    let currentGroup = [];
    let lastType = '';

    for (const importLine of sortedImports) {
      const moduleMatch = importLine.match(/from\s+['"]([^'"]+)['"]/);
      if (moduleMatch) {
        const moduleName = moduleMatch[1];
        let type = '';

        if (!moduleName.startsWith('.') && !moduleName.startsWith('@/')) {
          type = 'external';
        } else if (moduleName.startsWith('@/')) {
          type = 'internal';
        } else {
          type = 'relative';
        }

        if (lastType && lastType !== type) {
          groupedImports.push(...currentGroup, '');
          currentGroup = [importLine];
        } else {
          currentGroup.push(importLine);
        }

        lastType = type;
      }
    }

    if (currentGroup.length > 0) {
      groupedImports.push(...currentGroup);
    }

    // 重新组合文件内容
    const newContent = [...groupedImports, '', ...nonImportLines].join('\n');

    if (newContent !== content) {
      fs.writeFileSync(filePath, newContent, 'utf8');
      console.log(`✅ 修复导入: ${filePath}`);
      modified = true;
    }

    return modified;
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🚀 开始修复导入问题...\n');

  // 获取所有需要修复的文件
  const extensions = ['.ts', '.tsx'];
  const directories = ['src/components', 'src/hooks', 'src/app', 'src/lib'];

  let totalFixed = 0;

  for (const dir of directories) {
    if (!fs.existsSync(dir)) continue;

    function processDirectory(dirPath) {
      const items = fs.readdirSync(dirPath);

      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const stat = fs.statSync(fullPath);

        if (
          stat.isDirectory() &&
          !item.startsWith('.') &&
          item !== 'node_modules'
        ) {
          processDirectory(fullPath);
        } else if (
          stat.isFile() &&
          extensions.some((ext) => item.endsWith(ext))
        ) {
          if (fixImports(fullPath)) {
            totalFixed++;
          }
        }
      }
    }

    processDirectory(dir);
  }

  console.log(`\n✅ 完成！共修复了 ${totalFixed} 个文件的导入问题`);

  // 运行 ESLint 检查结果
  console.log('\n🔍 检查修复结果...');
  try {
    const result1 = execSync('pnpm lint 2>&1 | grep -c "import/order"', {
      encoding: 'utf8',
    });
    console.log(`剩余导入顺序错误: ${result1.trim()}`);

    const result2 = execSync(
      'pnpm lint 2>&1 | grep -c "no-duplicate-imports"',
      { encoding: 'utf8' }
    );
    console.log(`剩余重复导入错误: ${result2.trim()}`);
  } catch {
    console.log('检查完成');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixImports };
