'use client';

import { motion } from 'framer-motion';
import { Globe, Moon, Search, Shield, Smartphone, Zap } from 'lucide-react';
import { useTranslations } from 'next-intl';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

/**
 * 特性数据配置
 */
const features = [
  {
    id: 'responsive',
    icon: Smartphone,
    color: 'text-blue-500',
    bgColor: 'bg-blue-500/10',
  },
  {
    id: 'accessible',
    icon: Shield,
    color: 'text-green-500',
    bgColor: 'bg-green-500/10',
  },
  {
    id: 'performance',
    icon: Zap,
    color: 'text-yellow-500',
    bgColor: 'bg-yellow-500/10',
  },
  {
    id: 'seo',
    icon: Search,
    color: 'text-purple-500',
    bgColor: 'bg-purple-500/10',
  },
  {
    id: 'i18n',
    icon: Globe,
    color: 'text-cyan-500',
    bgColor: 'bg-cyan-500/10',
  },
  {
    id: 'darkMode',
    icon: Moon,
    color: 'text-indigo-500',
    bgColor: 'bg-indigo-500/10',
  },
];

/**
 * 特性展示组件
 */
export function FeaturesSection(): React.JSX.Element {
  const t = useTranslations('home.features');

  return (
    <section className="py-20 md:py-32">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            {t('title')}
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {t('description')}
          </p>
        </motion.div>
      </div>
    </section>
  );
}
