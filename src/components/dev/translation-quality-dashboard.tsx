'use client';

import {
  AlertTriangle,
  CheckCircle,
  Download,
  RefreshCw,
  XCircle,
} from 'lucide-react';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useTranslationQuality } from '@/hooks/use-translation-quality';
import {
  IssueSeverity,
  TranslationIssue,
} from '@/lib/i18n/translation-quality-manager';

/**
 * 翻译质量管理仪表板组件
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

interface TranslationQualityDashboardProps {
  className?: string;
}

export function TranslationQualityDashboard({
  className,
}: TranslationQualityDashboardProps): React.JSX.Element {
  const { qualityReport, isLoading, error, refreshReport, exportReport } =
    useTranslationQuality();

  const getSeverityIcon = (severity: IssueSeverity): React.JSX.Element => {
    switch (severity) {
      case 'critical':
        return <XCircle className='h-4 w-4 text-red-500' />;
      case 'high':
        return <AlertTriangle className='h-4 w-4 text-orange-500' />;
      case 'medium':
        return <AlertTriangle className='h-4 w-4 text-yellow-500' />;
      case 'low':
        return <CheckCircle className='h-4 w-4 text-blue-500' />;
      default:
        return <CheckCircle className='h-4 w-4 text-gray-500' />;
    }
  };

  const getSeverityColor = (severity: IssueSeverity): string => {
    switch (severity) {
      case 'critical':
        return 'destructive';
      case 'high':
        return 'destructive';
      case 'medium':
        return 'secondary';
      case 'low':
        return 'outline';
      default:
        return 'outline';
    }
  };

  if (isLoading) {
    return (
      <div className={`p-6 ${className ?? ''}`}>
        <div className='flex items-center justify-center'>
          <RefreshCw className='h-6 w-6 animate-spin' />
          <span className='ml-2'>加载翻译质量报告...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-6 ${className ?? ''}`}>
        <Card>
          <CardContent className='pt-6'>
            <div className='text-center text-red-500'>
              <XCircle className='mx-auto mb-2 h-8 w-8' />
              <p>加载翻译质量报告失败</p>
              <Button
                variant='outline'
                size='sm'
                onClick={refreshReport}
                className='mt-2'
              >
                <RefreshCw className='mr-2 h-4 w-4' />
                重试
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (qualityReport == null) {
    return (
      <div className={`p-6 ${className ?? ''}`}>
        <Card>
          <CardContent className='pt-6'>
            <div className='text-muted-foreground text-center'>
              <p>暂无翻译质量数据</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className ?? ''}`}>
      {/* 质量概览 */}
      <div className='grid grid-cols-1 gap-4 md:grid-cols-3'>
        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-sm font-medium'>总体评分</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {qualityReport.overallScore.toFixed(1)}
            </div>
            <p className='text-muted-foreground text-xs'>满分 100</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-sm font-medium'>覆盖率</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {(qualityReport.coverage * 100).toFixed(1)}%
            </div>
            <p className='text-muted-foreground text-xs'>翻译完整性</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-sm font-medium'>问题数量</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {qualityReport.issues.length}
            </div>
            <p className='text-muted-foreground text-xs'>待解决问题</p>
          </CardContent>
        </Card>
      </div>

      {/* 操作按钮 */}
      <div className='flex gap-2'>
        <Button onClick={refreshReport} variant='outline' size='sm'>
          <RefreshCw className='mr-2 h-4 w-4' />
          刷新报告
        </Button>
        <Button onClick={exportReport} variant='outline' size='sm'>
          <Download className='mr-2 h-4 w-4' />
          导出报告
        </Button>
      </div>

      {/* 问题列表 */}
      {qualityReport.issues.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>翻译问题</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-3'>
              {qualityReport.issues.map(
                (issue: TranslationIssue, index: number) => (
                  <div
                    key={index}
                    className='flex items-start gap-3 rounded-lg border p-3'
                  >
                    {getSeverityIcon(issue.severity)}
                    <div className='min-w-0 flex-1'>
                      <div className='mb-1 flex items-center gap-2'>
                        <Badge variant={getSeverityColor(issue.severity)}>
                          {issue.severity}
                        </Badge>
                        <span className='text-sm font-medium'>
                          {issue.type}
                        </span>
                      </div>
                      <p className='text-muted-foreground mb-1 text-sm'>
                        {issue.message}
                      </p>
                      <p className='text-muted-foreground text-xs'>
                        位置: {issue.location}
                      </p>
                    </div>
                  </div>
                )
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
