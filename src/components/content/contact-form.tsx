'use client';

import { type ReactElement, useMemo } from 'react';
import { useTranslations } from 'next-intl';
import { ContactFormContainer } from '@/components/forms/contact/contact-form-container';

/**
 * 联系表单组件
 *
 * 特性：
 * - 低摩擦度设计：仅2个必填项（姓名、邮箱）
 * - 可选项：沟通事项、地区、附加信息
 * - 多语言支持：支持中英文切换
 * - 响应式设计：适配移动端和桌面端
 * - 现代化UI：使用 shadcn/ui 组件
 * - 企业级验证：React Hook Form + Zod 验证系统
 * - 用户体验优化：清晰的标签和占位符
 * - 模块化架构：组件、Hook、类型分离
 */
export function ContactForm(): ReactElement {
  const t = useTranslations('pages.contact.form');

  // 使用useMemo缓存翻译函数，避免子组件不必要的重渲染
  const memoizedT = useMemo(() => t, [t]);

  return <ContactFormContainer memoizedT={memoizedT} />;
}
