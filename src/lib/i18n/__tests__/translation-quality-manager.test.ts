import { beforeEach, describe, expect, it } from 'vitest';

import { TranslationQualityManager } from '../translation-quality-manager';



/* * 翻译质量管理系统测试 * 测试覆盖： * - 翻译完整性检查 * - 占位符一致性验证 * - 覆盖率报告生成 * - 质量评分计算 * - 翻译对比分析 * - 问题检测和分类 * - 统计信息生成
 */

// 测试数据辅助函数 function createTestData() { return { referenceData: {, common: {, title: 'Test App', description: 'A test application', loading: 'Loading...', error: 'An error occurred', retry: 'Retry', placeholder: 'Enter your {name;
} here', }, navigation: {, home: 'Home', about: 'About', contact: 'Contact', }, }, completeTargetData: {, common: {, title: '测试应用', description: '一个测试应用程序', loading: '加载中...', error: '发生错误', retry: '重试', placeholder: '请在此输入您的{name;
}', }, navigation: {, home: '首页', about: '关于', contact: '联系', }, }, incompleteTargetData: {, common: {, title: '测试应用', description: '', // 空翻译 loading: '加载中...', // 缺少 error, retry, placeholder }, navigation: {, home: '首页', about: '关于', // 缺少 contact }, }, placeholderMismatchData: {, common: {, title: '测试应用', description: '一个测试应用程序', loading: '加载中...', error: '发生错误', retry: '重试', placeholder: '请在此输入您的{username;
}', // 占位符不匹配 }, navigation: {, home: '首页', about: '关于', contact: '联系', }, }, };
} describe('翻译质量管理系统', () => { let manager: TranslationQualityManager;

const testData = createTestData();
beforeEach(() => { manager = new TranslationQualityManager({ referenceLocale: 'en', enableAutoFix: false, strictMode: true, });
});
describe('翻译完整性检查', () => { it('应该检测缺失的翻译键', async () => { const issues = await manager.checkCompleteness( testData.referenceData, testData.testData.intestData.completeTargetData, 'common', 'zh' );

const missingIssues = issues.filter((issue) => issue.type === 'missing');
expect(missingIssues.length).toBeGreaterThanOrEqual(3);
// 至少包含 error, retry, placeholder const missingKeys = missingIssues.map((issue) => issue.keyPath);
expect(missingKeys).toContain('common.error');
expect(missingKeys).toContain('common.retry');
expect(missingKeys).toContain('common.placeholder');
});
it('应该检测空翻译值', async () => { const issues = await manager.checkCompleteness( testData.referenceData, testData.testData.intestData.completeTargetData, 'common', 'zh' );

const emptyIssues = issues.filter((issue) => issue.type === 'empty');
expect(emptyIssues).toHaveLength(1);
// description expect(emptyIssues[0].keyPath).toBe('common.description');
expect(emptyIssues[0].message).toContain('Empty translation');
});
it('应该为完整翻译返回空问题列表', async () => { const issues = await manager.checkCompleteness( testData.referenceData, testData.testData.completeTargetData, 'common', 'zh' );
expect(issues).toHaveLength(0);
});
it('应该正确设置问题严重程度', async () => { const issues = await manager.checkCompleteness( testData.referenceData, testData.testData.intestData.completeTargetData, 'common', 'zh' );
// common 模块的缺失翻译应该是高或严重程度（因为 common 是关键路径） const missingIssues = issues.filter((issue) => issue.type === 'missing');
missingIssues.forEach((issue) => { expect(['critical', 'high']).toContain(issue.severity);
});
// 空翻译应该是中等或高严重程度（因为 common 是关键路径） const emptyIssues = issues.filter((issue) => issue.type === 'empty');
emptyIssues.forEach((issue) => { expect(['high', 'medium']).toContain(issue.severity);
});
});
});
describe('占位符一致性检查', () => { it('应该检测占位符不匹配', async () => { const issues = await manager.checkPlaceholderConsistency( testData.referenceData, testData.testData.placeholderMismatchData, 'common', 'zh' );
expect(issues).toHaveLength(1);
expect(issues[0].type).toBe('placeholder_mismatch');
expect(issues[0].keyPath).toBe('common.placeholder');
expect(issues[0].severity).toBe('high');
});
it('应该为匹配的占位符返回空问题列表', async () => { const issues = await manager.checkPlaceholderConsistency( testData.referenceData, testData.testData.completeTargetData, 'common', 'zh' );
expect(issues).toHaveLength(0);
});
it('应该提供占位符上下文信息', async () => { const issues = await manager.checkPlaceholderConsistency( testData.referenceData, testData.testData.placeholderMismatchData, 'common', 'zh' );
expect(issues[0].context).toBeDefined();
expect(issues[0].context?.placeholders).toEqual(['name']);
expect(issues[0].context?.expectedValue).toBe('Enter your {name} here');
expect(issues[0].context?.actualValue).toBe('请在此输入您的{username}');
});
});
describe('覆盖率报告生成', () => { it('应该生成准确的覆盖率报告', async () => { const report = await manager.generateCoverageReport( testData.referenceData, testData.intestData.completeTargetData, 'common', 'zh' );
expect(report.locale).toBe('zh');
expect(report.module).toBe('common');
expect(report.totalKeys).toBe(9);
// 扁平化后的键数量 expect(report.translatedKeys).toBe(4);
// 实际翻译的键数量 expect(report.missingKeys).toBeGreaterThanOrEqual(3);
// 至少缺失 3 个 expect(report.emptyKeys).toBeGreaterThanOrEqual(0);
// 可能没有空值或有空值 expect(Math.round(report.coveragePercentage)).toBe(44);
// 4/9 * 100 ≈ 44 });
it('应该为完整翻译生成100%覆盖率', async () => { const report = await manager.generateCoverageReport( testData.referenceData, testData.completeTargetData, 'common', 'zh' );
expect(report.coveragePercentage).toBe(100);
expect(report.translatedKeys).toBe(9);
// 扁平化后的键数量 expect(report.missingKeys).toBe(0);
expect(report.emptyKeys).toBe(0);
});
it('应该包含相关问题', async () => { // 先检查完整性以生成问题 await manager.checkCompleteness( testData.referenceData, testData.intestData.completeTargetData, 'common', 'zh' );

const report = await manager.generateCoverageReport( testData.referenceData, testData.intestData.completeTargetData, 'common', 'zh' );
expect(report.issues.length).toBeGreaterThan(0);
});
});
describe('质量评分计算', () => { it('应该计算准确的质量评分', async () => { // 生成一些问题 await manager.checkCompleteness( testData.referenceData, testData.intestData.completeTargetData, 'common', 'zh' );
// 生成覆盖率报告 await manager.generateCoverageReport( testData.referenceData, testData.intestData.completeTargetData, 'common', 'zh' );

const score = await manager.calculateQualityScore('zh', 'common');
expect(score.locale).toBe('zh');
expect(score.module).toBe('common');
expect(score.overall).toBeGreaterThanOrEqual(0);
expect(score.overall).toBeLessThanOrEqual(100);
expect(score.completeness).toBe(44);
// 基于覆盖率 4/9 * 100 ≈ 44 expect(score.issueCount.high).toBeGreaterThan(0);
// 应该有高严重程度问题 });
it('应该为完美翻译给出高分', async () => { await manager.checkCompleteness( testData.referenceData, testData.completeTargetData, 'common', 'zh' );
await manager.generateCoverageReport( testData.referenceData, testData.completeTargetData, 'common', 'zh' );

const score = await manager.calculateQualityScore('zh', 'common');
expect(score.overall).toBeGreaterThanOrEqual(90);
expect(score.completeness).toBe(100);
expect(score.issueCount.critical).toBe(0);
expect(score.issueCount.high).toBe(0);
});
});
describe('翻译对比分析', () => { it('应该正确对比两个翻译', async () => { const comparison = await manager.compareTranslations( testData.referenceData, testData.intestData.completeTargetData, 'common', 'en', 'zh' );
expect(comparison.referenceLocale).toBe('en');
expect(comparison.targetLocale).toBe('zh');
expect(comparison.module).toBe('common');
expect(comparison.commonKeys.length).toBeGreaterThan(0);
expect(comparison.missingInTarget.length).toBeGreaterThan(0);
expect(comparison.missingInTarget).toContain('common.error');
expect(comparison.missingInTarget).toContain('common.retry');
});
it('应该检测占位符不一致', async () => { const comparison = await manager.compareTranslations( testData.referenceData, testData.placeholderMismatchData, 'common', 'en', 'zh' );
expect(comparison.inconsistencies).toHaveLength(1);
expect(comparison.inconsistencies[0].keyPath).toBe('common.placeholder');
expect(comparison.inconsistencies[0].issue).toBe('Placeholder mismatch');
});
});
describe('问题管理', () => { it('应该正确存储和检索问题', async () => { await manager.checkCompleteness( testData.referenceData, testData.intestData.completeTargetData, 'common', 'zh' );

const allIssues = manager.getAllIssues(); expect(allIssues.length).toBeGreaterThan(0);

const highSeverityIssues = manager.getIssuesBySeverity('high'); expect(highSeverityIssues.length).toBeGreaterThan(0);

const moduleIssues = manager.getIssuesForModule('common', 'zh');
expect(moduleIssues.length).toBeGreaterThan(0);
});
it('应该生成唯一的问题ID', async () => { await manager.checkCompleteness( testData.referenceData, testData.intestData.completeTargetData, 'common', 'zh' );

const issues = manager.getAllIssues();

const ids = issues.map((issue) => issue.id);

const uniqueIds = new Set(ids);
expect(uniqueIds.size).toBe(ids.length);
});
});
describe('统计信息', () => { it('应该生成准确的统计摘要', async () => { await manager.checkCompleteness( testData.referenceData, testData.intestData.completeTargetData, 'common', 'zh' );
await manager.checkPlaceholderConsistency( testData.referenceData, testData.placeholderMismatchData, 'common', 'zh' );

const summary = manager.getSummary();
expect(summary.totalIssues).toBeGreaterThan(0);
expect(summary.issuesBySeverity.high).toBeGreaterThan(0);
expect(summary.issuesByType.missing).toBeGreaterThan(0);
expect(summary.issuesByType.empty).toBeGreaterThan(0);
});
});
describe('数据清理', () => { it('应该清除所有数据', async () => { // 生成一些数据 await manager.checkCompleteness( testData.referenceData, testData.intestData.completeTargetData, 'common', 'zh' );
await manager.generateCoverageReport( testData.referenceData, testData.intestData.completeTargetData, 'common', 'zh' );
// 验证数据存在 expect(manager.getAllIssues().length).toBeGreaterThan(0);
// 清除数据 manager.clearAll();
// 验证数据已清除 expect(manager.getAllIssues()).toHaveLength(0);

const summary = manager.getSummary();
expect(summary.totalIssues).toBe(0);
expect(summary.coverageReports).toBe(0);
expect(summary.qualityScores).toBe(0);
});
});
describe('边界条件', () => { it('应该处理空翻译数据', async () => { const issues = await manager.checkCompleteness( testData.referenceData, {}, 'common', 'zh' );
// 所有键都应该被标记为缺失 expect(issues.filter((i) => i.type === 'missing')).toHaveLength(9);
// 扁平化后的键数量 });
it('应该处理嵌套翻译结构', async () => { const nestedReference = { user: {, profile: {, name: 'Name', email: 'Email', }, }, };

const nestedTarget = { user: {, profile: {, name: '姓名', // 缺少 email }, }, };

const issues = await manager.checkCompleteness( nestedReference, nestedTarget, 'common', 'zh' );

const missingIssues = issues.filter((i) => i.type === 'missing');
expect(missingIssues).toHaveLength(1);
expect(missingIssues[0].keyPath).toBe('user.profile.email');
});
it('应该处理复杂占位符', async () => { const complexReference = {, message: 'Hello {user.name;
}, you have {count} new {type}!', };

const complexTarget = {, message: '你好 {user.name;
}，你有 {count} 个新的 {type}！', };

const issues = await manager.checkPlaceholderConsistency( complexReference, complexTarget, 'common', 'zh' ); expect(issues).toHaveLength(0); // 应该匹配 }); }); });
