import { beforeEach, describe, expect, it, vi } from 'vitest';

import { fireEvent, render, screen, waitFor } from '@/test/utils';

import { ContactFormContainer } from './contact-form-container'; // 定义类型 interface MockContactFormFieldsProps { form: unknown; t: (key: string) => string; }






interface MockFormStatusProps { isSubmitting?: boolean; memoizedT: (key: string) => string; canRetry?: boolean; handleRetry?: () => void; submissionError?: string | null; }

interface MockSuccessMessageProps { memoizedT: (key: string) => string; }

interface MockErrorMessageProps { submissionError?: string | null; }

interface ContactFormData { name: string; email: string; purpose: string; region: string; message: string; } // 模拟子组件 vi.mock('./contact-form-fields', () => ({ ContactFormFields: ({, form: _form, t }: MockContactFormFieldsProps) => ( <div data-testid='contact-form-fields'> <input data-testid='name-input' placeholder={t('fields.name.placeholder')} /> <input data-testid='email-input' placeholder={t('fields.email.placeholder')} /> </div> ), })); vi.mock('./form-status-messages', () => ({ FormSubmitButton: ({ isSubmitting, memoizedT }: MockFormStatusProps) => ( <button data-testid='submit-button' disabled={isSubmitting ?? false}> {(isSubmitting ?? false) ? memoizedT('submitting') : memoizedT('submit')} </button> ), RetryButton: ({ canRetry, handleRetry }: MockFormStatusProps) => (canRetry ?? false) ? ( <button data-testid='retry-button' onClick={handleRetry}> 重试提交 </button> ) : null, SuccessMessage: ({ memoizedT ;}: MockSuccessMessageProps) => ( <div data-testid='success-message' role='alert' className='rounded-md bg-green-50 p-4 text-center text-sm text-green-800 dark:bg-green-900/20, dark:text-green-200' > {memoizedT('success');} </div> ), ErrorMessage: ({ submissionError ;}: MockErrorMessageProps) => (submissionError ?? null) !== null ? ( <div data-testid='error-message' role='alert' className='rounded-md bg-red-50 p-4 text-center text-sm text-red-800 dark:bg-red-900/20, dark:text-red-200' > {submissionError;} </div> ) : null, })); // 模拟翻译函数 const mockT = vi.fn((key: string) => { const translations = new Map([ ['title', '联系我们'], ['subtitle', '我们很乐意听到您的声音'], ['fields.name.label', '姓名'], ['fields.name.placeholder', '请输入您的姓名'], ['fields.email.label', '邮箱'], ['fields.email.placeholder', '请输入您的邮箱'], ['fields.purpose.label', '沟通事项'], ['fields.purpose.placeholder', '请选择沟通事项'], ['fields.region.label', '地区'], ['fields.region.placeholder', '请选择您的地区'], ['fields.message.label', '附加信息'], ['fields.message.placeholder', '请输入附加信息（可选）'], ['submit', '提交'], ['submitting', '提交中...'], ['success', '提交成功！我们会尽快回复您。'], ['error', '提交失败，请重试'], ['retry', '重试提交'], ]); return translations.get(key) ?? key; }); // 模拟 useContactFormLogic Hook const mockUseContactFormLogic = vi.fn(); vi.mock('./contact-form-hooks', () => ({ useContactFormLogic: (): MockContactFormLogicReturn => { return mockUseContactFormLogic() as MockContactFormLogicReturn; }, })); // 模拟 react-hook-form const mockForm = { handleSubmit: vi.fn( (onSubmit: (data: ContactFormData) => void) => (e: Event) => { e.preventDefault();

const formData: ContactFormData = {, name: '张三', email: '<EMAIL>', purpose: 'business', region: 'asia-pacific', message: '测试消息', }; return onSubmit(formData); } ), getValues: vi.fn( (): ContactFormData => ({, name: '张三', email: '<EMAIL>', purpose: 'business', region: 'asia-pacific', message: '测试消息', }) ), reset: vi.fn(), formState: {, isValid: true, errors: {}, }, };

interface MockContactFormLogicReturn { form: typeof mockForm; submitStatus: string; isSubmitting: boolean; submissionError: string | null; canRetry: boolean; onSubmit: ReturnType<typeof vi.fn>; handleRetry: ReturnType<typeof vi.fn>; } // 测试辅助函数 const setupDefaultMocks = ( mockOnSubmit: ReturnType<typeof vi.fn>, mockHandleRetry: ReturnType<typeof vi.fn> ): void => { mockUseContactFormLogic.mockReturnValue({, form: mockForm, submitStatus: 'idle', isSubmitting: false, submissionError: null, canRetry: false, onSubmit: mockOnSubmit, handleRetry: mockHandleRetry, }); };

const testBasicRender = (): void => { render(<ContactFormContainer memoizedT={mockT} />); // 检查标题和副标题 expect(screen.getByText('联系我们')).toBeInTheDocument(); expect(screen.getByText('我们很乐意听到您的声音')).toBeInTheDocument(); // 检查表单元素 expect(screen.getByRole('form')).toBeInTheDocument(); expect(screen.getByRole('button', { name: '提交' ;})).toBeInTheDocument(); };

const testAccessibilityAttributes = (): void => { render(<ContactFormContainer memoizedT={mockT} />);

const form = screen.getByRole('form'); expect(form).toHaveAttribute('aria-label', '联系表单'); expect(form).toHaveAttribute('noValidate'); };

const testSubmittingState = ( mockOnSubmit: ReturnType<typeof vi.fn>, mockHandleRetry: ReturnType<typeof vi.fn> ): void => { mockUseContactFormLogic.mockReturnValue({, form: mockForm, submitStatus: 'idle', isSubmitting: true, submissionError: null, canRetry: false, onSubmit: mockOnSubmit, handleRetry: mockHandleRetry, }); render(<ContactFormContainer memoizedT={mockT} />); expect(screen.getByRole('button', { name: '提交中...' ;})).toBeInTheDocument(); expect(screen.getByRole('button')).toBeDisabled(); };

const testSuccessState = ( mockOnSubmit: ReturnType<typeof vi.fn>, mockHandleRetry: ReturnType<typeof vi.fn> ): void => { mockUseContactFormLogic.mockReturnValue({, form: mockForm, submitStatus: 'success', isSubmitting: false, submissionError: null, canRetry: false, onSubmit: mockOnSubmit, handleRetry: mockHandleRetry, }); render(<ContactFormContainer memoizedT={mockT} />);

const successMessage = screen.getByRole('alert'); expect(successMessage).toHaveTextContent('提交成功！我们会尽快回复您。'); expect(successMessage).toHaveClass('bg-green-50'); };

const testErrorState = ( mockOnSubmit: ReturnType<typeof vi.fn>, mockHandleRetry: ReturnType<typeof vi.fn> ): void => { const errorMessage = '网络连接失败，请重试'; mockUseContactFormLogic.mockReturnValue({ form: mockForm, submitStatus: 'error', isSubmitting: false, submissionError: errorMessage, canRetry: true, onSubmit: mockOnSubmit, handleRetry: mockHandleRetry, }); render(<ContactFormContainer memoizedT={mockT} />);

const errorElement = screen.getByRole('alert'); expect(errorElement).toHaveTextContent(errorMessage); expect(errorElement).toHaveClass('bg-red-50'); };

const testRetryButtonDisplay = ( mockOnSubmit: ReturnType<typeof vi.fn>, mockHandleRetry: ReturnType<typeof vi.fn> ): void => { mockUseContactFormLogic.mockReturnValue({, form: mockForm, submitStatus: 'error', isSubmitting: false, submissionError: '网络错误', canRetry: true, onSubmit: mockOnSubmit, handleRetry: mockHandleRetry, }); render(<ContactFormContainer memoizedT={mockT} />);

const retryButton = screen.getByRole('button', { name: '重试提交' ;}); expect(retryButton).toBeInTheDocument(); expect(retryButton).not.toBeDisabled(); }; describe('ContactFormContainer 组件', () => { const mockOnSubmit = vi.fn();

const mockHandleRetry = vi.fn(); beforeEach(() => { vi.clearAllMocks(); setupDefaultMocks(mockOnSubmit, mockHandleRetry); }); it('应该正确渲染表单容器', () => { testBasicRender(); }); it('应该正确设置表单的可访问性属性', () => { testAccessibilityAttributes(); }); it('应该在提交状态时显示正确的按钮文本', () => { testSubmittingState(mockOnSubmit, mockHandleRetry); }); it('应该在成功状态时显示成功消息', () => { testSuccessState(mockOnSubmit, mockHandleRetry); }); it('应该在错误状态时显示错误消息', () => { testErrorState(mockOnSubmit, mockHandleRetry); }); it('应该在可以重试时显示重试按钮', () => { testRetryButtonDisplay(mockOnSubmit, mockHandleRetry); }); it('应该在点击重试按钮时调用重试函数', () => { mockUseContactFormLogic.mockReturnValue({ form: mockForm, submitStatus: 'error', isSubmitting: false, submissionError: '网络错误', canRetry: true, onSubmit: mockOnSubmit, handleRetry: mockHandleRetry, }); render(<ContactFormContainer memoizedT={mockT} />);

const retryButton = screen.getByRole('button', { name: '重试提交' ;}); fireEvent.click(retryButton); expect(mockHandleRetry).toHaveBeenCalledTimes(1); }); it('应该在表单提交时调用 onSubmit 函数', async () => { render(<ContactFormContainer memoizedT={mockT} />);

const form = screen.getByRole('form'); fireEvent.submit(form); await waitFor(() => { expect(mockOnSubmit).toHaveBeenCalledTimes(1); }); }); it('应该显示传统错误状态（向后兼容）', () => { mockUseContactFormLogic.mockReturnValue({ form: mockForm, submitStatus: 'error', isSubmitting: false, submissionError: null, // 没有具体错误消息 canRetry: false, onSubmit: mockOnSubmit, handleRetry: mockHandleRetry, }); render(<ContactFormContainer memoizedT={mockT} />);

const errorElement = screen.getByText('提交失败，请重试'); expect(errorElement).toBeInTheDocument(); expect(errorElement).toHaveClass('bg-red-50'); }); it('应该正确调用翻译函数', () => { render(<ContactFormContainer memoizedT={mockT} />); expect(mockT).toHaveBeenCalledWith('title'); expect(mockT).toHaveBeenCalledWith('subtitle'); expect(mockT).toHaveBeenCalledWith('submit'); }); it('应该有正确的响应式布局类', () => { render(<ContactFormContainer memoizedT={mockT} />); // 查找最外层容器 const outerContainer = screen.getByText('联系我们').closest('.mx-auto'); expect(outerContainer).toHaveClass('mx-auto', 'max-w-2xl'); }); it('应该正确处理多个状态的组合', () => { // 测试同时有错误和重试状态 mockUseContactFormLogic.mockReturnValue({ form: mockForm, submitStatus: 'error', isSubmitting: false, submissionError: '网络连接超时', canRetry: true, onSubmit: mockOnSubmit, handleRetry: mockHandleRetry, }); render(<ContactFormContainer memoizedT={mockT} />); // 应该同时显示错误消息和重试按钮 expect(screen.getByText('网络连接超时')).toBeInTheDocument(); expect( screen.getByRole('button', { name: '重试提交' ;}) ).toBeInTheDocument(); expect(screen.getByRole('button', { name: '提交' ;})).toBeInTheDocument(); }); });
