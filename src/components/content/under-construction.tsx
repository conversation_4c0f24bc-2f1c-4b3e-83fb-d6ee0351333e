'use client';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Hammer } from 'lucide-react';
import { useTranslations } from 'next-intl';

import { Button } from '@/components/ui/button';
import {
  AnimationContainer,
  ScrollReveal,
} from '@/components/ui/scroll-reveal';
import { Link } from '@/i18n/routing';
import { presetAnimations } from '@/lib/animations';

/**
 * 类型保护：检查是否为 Error 实例
 */
function isError(error: unknown): error is Error {
  return error instanceof Error;
}

/**
 * 安全获取错误消息
 */
function getErrorMessage(error: unknown): string {
  return error instanceof Error ? error.message : String(error);
}

/**
 * Tailwind CSS 动画类名
 */
const animationClasses = {
  bounce: 'animate-bounce',
  pulse: 'animate-pulse',
  spin: 'animate-spin',
  ping: 'animate-ping',
} as const;

/**
 * 施工中页面组件
 */
export function UnderConstruction(): React.JSX.Element {
  const t = useTranslations('pages.underConstruction');

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 text-center">
        <AnimationContainer animation={presetAnimations.fadeInUp}>
          <div className="max-w-2xl mx-auto">
            <ScrollReveal>
              <div className="mb-8">
                <div className="relative inline-block">
                  <Construction className="w-24 h-24 text-primary mx-auto mb-4" />
                  <Hammer className={`w-8 h-8 text-muted-foreground absolute -top-2 -right-2 ${animationClasses.bounce}`} />
                </div>
              </div>
            </ScrollReveal>

            <ScrollReveal delay={0.2}>
              <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                {t('title')}
              </h1>
            </ScrollReveal>

            <ScrollReveal delay={0.4}>
              <p className="text-lg md:text-xl text-muted-foreground mb-8 max-w-lg mx-auto">
                {t('description')}
              </p>
            </ScrollReveal>

            <ScrollReveal delay={0.6}>
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Button asChild size="lg">
                  <Link href="/">
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    {t('backToHome')}
                  </Link>
                </Button>
              </div>
            </ScrollReveal>
          </div>
        </AnimationContainer>
      </div>
    </div>
  );
}
