#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * 修复语法错误的脚本
 */

function fixSyntaxErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 1. 修复注释和代码在同一行的问题
    const commentCodePattern = /\/\*\*([^*]|\*(?!\/))*\*\/\s*export/g;
    const newContent1 = content.replace(commentCodePattern, (match) => {
      const parts = match.split('*/');
      if (parts.length === 2) {
        return parts[0] + '*/\n' + parts[1].trim();
      }
      return match;
    });
    
    if (newContent1 !== content) {
      content = newContent1;
      modified = true;
    }
    
    // 2. 修复单行注释和代码在同一行的问题
    const singleCommentPattern = /\/\/.*?export/g;
    const newContent2 = content.replace(singleCommentPattern, (match) => {
      const commentEnd = match.indexOf('export');
      if (commentEnd > 0) {
        return match.substring(0, commentEnd) + '\nexport';
      }
      return match;
    });
    
    if (newContent2 !== content) {
      content = newContent2;
      modified = true;
    }
    
    // 3. 修复缺少分号的问题
    const lines = content.split('\n');
    const fixedLines = lines.map(line => {
      const trimmed = line.trim();
      
      // 如果是导入语句但没有分号
      if (trimmed.startsWith('import ') && !trimmed.endsWith(';') && !trimmed.endsWith('{')) {
        return line + ';';
      }
      
      // 如果是导出语句但没有分号
      if (trimmed.startsWith('export ') && !trimmed.endsWith(';') && !trimmed.endsWith('{') && !trimmed.includes('function') && !trimmed.includes('class')) {
        return line + ';';
      }
      
      return line;
    });
    
    const newContent3 = fixedLines.join('\n');
    if (newContent3 !== content) {
      content = newContent3;
      modified = true;
    }
    
    // 4. 修复缺少导入语句开头的问题
    const fixedLines2 = content.split('\n').map(line => {
      const trimmed = line.trim();
      
      // 如果行以 } from 开始，说明缺少了 import {
      if (trimmed.startsWith('} from ')) {
        return line.replace('} from ', 'import { } from ');
      }
      
      return line;
    });
    
    const newContent4 = fixedLines2.join('\n');
    if (newContent4 !== content) {
      content = newContent4;
      modified = true;
    }
    
    // 5. 修复多行注释格式
    const multilineCommentPattern = /\/\*\*\s*\*\s*([^*]|\*(?!\/))*\*\//g;
    const newContent5 = content.replace(multilineCommentPattern, (match) => {
      // 如果注释是单行但格式错误
      if (!match.includes('\n')) {
        const commentContent = match.replace(/\/\*\*\s*\*?\s*/, '').replace(/\s*\*\//, '');
        return `/**\n * ${commentContent}\n */`;
      }
      return match;
    });
    
    if (newContent5 !== content) {
      content = newContent5;
      modified = true;
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 修复语法错误: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🚀 开始修复语法错误...\n');
  
  // 获取所有需要修复的文件
  const extensions = ['.ts', '.tsx'];
  const directories = ['src/components', 'src/hooks', 'src/app', 'src/lib'];
  
  let totalFixed = 0;
  
  for (const dir of directories) {
    if (!fs.existsSync(dir)) continue;
    
    function processDirectory(dirPath) {
      const items = fs.readdirSync(dirPath);
      
      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          processDirectory(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          if (fixSyntaxErrors(fullPath)) {
            totalFixed++;
          }
        }
      }
    }
    
    processDirectory(dir);
  }
  
  console.log(`\n✅ 完成！共修复了 ${totalFixed} 个文件的语法错误`);
  
  // 运行 ESLint 检查结果
  console.log('\n🔍 检查修复结果...');
  try {
    const result = execSync('pnpm lint 2>&1 | grep -c "Parsing error"', { encoding: 'utf8' });
    console.log(`剩余解析错误: ${result.trim()}`);
  } catch (error) {
    console.log('检查完成');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixSyntaxErrors };
