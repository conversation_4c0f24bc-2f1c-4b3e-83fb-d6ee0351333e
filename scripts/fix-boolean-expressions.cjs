#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * 批量修复严格布尔表达式的脚本
 */

function fixBooleanExpressions(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 1. 修复 nullable object 条件检查
    // obj && obj.prop => obj !== null && obj !== undefined && obj.prop
    const objectPatterns = [
      // 简单的对象检查: obj &&
      {
        pattern: /\b([a-zA-Z_$][a-zA-Z0-9_$]*)\s*&&\s*([a-zA-Z_$][a-zA-Z0-9_$]*\.[a-zA-Z_$][a-zA-Z0-9_$]*)/g,
        replacement: '$1 !== null && $1 !== undefined && $2'
      },
      // 对象属性检查: obj.prop &&
      {
        pattern: /\b([a-zA-Z_$][a-zA-Z0-9_$]*\.[a-zA-Z_$][a-zA-Z0-9_$]*)\s*&&/g,
        replacement: '$1 !== null && $1 !== undefined &&'
      }
    ];
    
    // 2. 修复 nullable string 条件检查
    // str && => str !== null && str !== undefined && str !== ''
    const stringPatterns = [
      // 字符串变量检查
      {
        pattern: /\b([a-zA-Z_$][a-zA-Z0-9_$]*)\s*&&\s*(?![a-zA-Z_$][a-zA-Z0-9_$]*\.)/g,
        replacement: '$1 !== null && $1 !== undefined && $1 !== \'\' &&'
      }
    ];
    
    // 3. 修复条件表达式中的非布尔值
    // if (value) => if (Boolean(value))
    const conditionalPatterns = [
      // if 语句
      {
        pattern: /if\s*\(\s*([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\s*\)/g,
        replacement: 'if (Boolean($1))'
      },
      // while 语句
      {
        pattern: /while\s*\(\s*([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\s*\)/g,
        replacement: 'while (Boolean($1))'
      }
    ];
    
    // 4. 修复三元运算符
    // value ? a : b => Boolean(value) ? a : b
    const ternaryPatterns = [
      {
        pattern: /\b([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\s*\?\s*/g,
        replacement: 'Boolean($1) ? '
      }
    ];
    
    // 5. 修复逻辑运算符 || 为 ??
    // value || defaultValue => value ?? defaultValue
    const nullishPatterns = [
      {
        pattern: /\b([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\s*\|\|\s*([^|&]+)/g,
        replacement: '$1 ?? $2'
      }
    ];
    
    // 应用修复模式（按顺序，避免冲突）
    const allPatterns = [
      ...objectPatterns,
      ...stringPatterns,
      ...nullishPatterns,
      // 注意：条件和三元运算符的修复可能过于激进，先注释掉
      // ...conditionalPatterns,
      // ...ternaryPatterns
    ];
    
    for (const { pattern, replacement } of allPatterns) {
      const newContent = content.replace(pattern, replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    }
    
    // 特殊处理：修复常见的条件检查模式
    const specificFixes = [
      // data && data.length => data !== null && data !== undefined && data.length > 0
      {
        pattern: /\b([a-zA-Z_$][a-zA-Z0-9_$]*)\s*&&\s*\1\.length\b/g,
        replacement: '$1 !== null && $1 !== undefined && $1.length > 0'
      },
      // error && error.message => error !== null && error !== undefined && error.message
      {
        pattern: /\berror\s*&&\s*error\.message\b/g,
        replacement: 'error !== null && error !== undefined && error.message'
      },
      // user && user.id => user !== null && user !== undefined && user.id
      {
        pattern: /\buser\s*&&\s*user\.id\b/g,
        replacement: 'user !== null && user !== undefined && user.id'
      },
      // config && config.value => config !== null && config !== undefined && config.value
      {
        pattern: /\bconfig\s*&&\s*config\.[a-zA-Z_$][a-zA-Z0-9_$]*\b/g,
        replacement: 'config !== null && config !== undefined && config.$1'
      }
    ];
    
    for (const { pattern, replacement } of specificFixes) {
      const newContent = content.replace(pattern, replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 修复布尔表达式: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🚀 开始批量修复严格布尔表达式...\n');
  
  // 获取所有需要修复的文件
  const extensions = ['.ts', '.tsx'];
  const directories = ['src/components', 'src/hooks', 'src/app', 'src/lib'];
  
  let totalFixed = 0;
  
  for (const dir of directories) {
    if (!fs.existsSync(dir)) continue;
    
    function processDirectory(dirPath) {
      const items = fs.readdirSync(dirPath);
      
      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          processDirectory(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          if (fixBooleanExpressions(fullPath)) {
            totalFixed++;
          }
        }
      }
    }
    
    processDirectory(dir);
  }
  
  console.log(`\n✅ 完成！共修复了 ${totalFixed} 个文件的布尔表达式`);
  
  // 运行 ESLint 检查结果
  console.log('\n🔍 检查修复结果...');
  try {
    execSync('pnpm lint 2>&1 | grep -c "strict-boolean-expressions"', { stdio: 'inherit' });
  } catch (error) {
    console.log('检查完成');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixBooleanExpressions };
