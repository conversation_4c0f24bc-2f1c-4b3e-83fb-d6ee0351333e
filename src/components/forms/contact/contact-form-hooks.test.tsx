import { act, renderHook } from '@testing-library/react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'; useContactFormConfig, useContactFormLogic,

import { } from './contact-form-hooks';




// 模拟 useFormSubmission Hook const mockSubmitWithErrorHandling = vi.fn();

const mockRetrySubmission = vi.fn();

const mockClearError = vi.fn(); vi.mock('@/hooks/use-form-submission', () => ({ useFormSubmission: vi.fn(() => ({, isSubmitting: false, error: null, canRetry: false, submitWithErrorHandling: mockSubmitWithErrorHandling, retrySubmission: mockRetrySubmission, clearError: mockClearError, })), })); // 常量定义 const TEST_EMAIL = '<EMAIL>';

const TEST_NAME = '张三'; // 测试辅助函数 const createTestData = (): { name: string; email: string; purpose: string; region: string; message: string; } => ({ name: TEST_NAME, email: TEST_EMAIL, purpose: 'business', region: 'asia-pacific', message: '测试消息', });

const setupMockSubmission = (): void => { mockSubmitWithErrorHandling.mockImplementation( async (submitFn: () => Promise<void>) => { await submitFn(); } ); }; // 测试辅助函数 const testInitialState = (): void => { const { result } = renderHook(() => useContactFormLogic()); expect(result.current.submitStatus).toBe('idle'); expect(result.current.isSubmitting).toBe(false); expect(result.current.submissionError).toBe(null); expect(result.current.canRetry).toBe(false); expect(typeof result.current.onSubmit).toBe('function'); expect(typeof result.current.handleRetry).toBe('function'); expect(result.current.form).toBeDefined(); };

const testFormDefaultValues = (): void => { const { result } = renderHook(() => useContactFormLogic());

const formValues = result.current.form.getValues(); expect(formValues).toEqual({ name: '', email: '', purpose: '', region: '', message: '', }); };

const testSubmitWithErrorHandling = async (): Promise<void> => { const { result } = renderHook(() => useContactFormLogic());

const testData = createTestData(); await act(async () => { await result.current.onSubmit(testData); }); expect(mockClearError).toHaveBeenCalledTimes(1); expect(mockSubmitWithErrorHandling).toHaveBeenCalledTimes(1); };

const testFormReset = async (): Promise<void> => { setupMockSubmission();

const { result } = renderHook(() => useContactFormLogic()); // 设置表单值 act(() => { result.current.form.setValue('name', TEST_NAME); result.current.form.setValue('email', TEST_EMAIL); });

const testData = createTestData(); await act(async () => { await result.current.onSubmit(testData); }); // 验证表单被重置 const formValues = result.current.form.getValues(); expect(formValues.name).toBe(''); expect(formValues.email).toBe(''); }; describe('ContactFormHooks', () => { beforeEach(() => { vi.clearAllMocks(); // 重置 Math.random 为可预测的值 vi.spyOn(Math, 'random').mockReturnValue(0.5); }); afterEach(() => { vi.restoreAllMocks(); }); describe('useContactFormLogic', () => { it('应该返回正确的初始状态', () => { testInitialState(); }); it('应该正确初始化表单默认值', () => { testFormDefaultValues(); }); it('应该在提交时调用 submitWithErrorHandling', async () => { await testSubmitWithErrorHandling(); }); it('应该在成功提交后重置表单', async () => { await testFormReset(); }); it('应该在重试时调用 retrySubmission', async () => { const { result } = renderHook(() => useContactFormLogic()); // 设置表单值 act(() => { result.current.form.setValue('name', TEST_NAME); result.current.form.setValue('email', TEST_EMAIL); }); await act(async () => { await result.current.handleRetry(); }); expect(mockRetrySubmission).toHaveBeenCalledTimes(1); }); it('应该处理模拟的网络错误', async () => { // 模拟网络错误（Math.random() > 0.7） vi.spyOn(Math, 'random').mockReturnValue(0.8); let capturedError: unknown = null; mockSubmitWithErrorHandling.mockImplementation( async (submitFn: () => Promise<void>) => { try { await submitFn(); } catch (error) { capturedError = error instanceof Error ? error : new Error(String(error)); throw error; } } );

const { result } = renderHook(() => useContactFormLogic());

const testData = createTestData(); await act(async () => { try { await result.current.onSubmit(testData); } catch { // 忽略错误，我们只关心错误是否被正确抛出 } }); expect(capturedError).toBeInstanceOf(Error); expect(capturedError).not.toBeNull(); if (capturedError instanceof Error) { expect(capturedError.message).toBe('模拟网络错误'); } }); it('应该正确处理表单状态', () => { const { result } = renderHook(() => useContactFormLogic()); // 检查初始状态 expect(result.current.form).toBeDefined(); expect(result.current.submitStatus).toBe('idle'); expect(result.current.isSubmitting).toBe(false); expect(result.current.submissionError).toBeNull(); expect(result.current.canRetry).toBe(false); // 检查表单字段可以设置值 act(() => { result.current.form.setValue('name', 'Test Name'); result.current.form.setValue('email', '<EMAIL>'); }); expect(result.current.form.getValues('name')).toBe('Test Name'); expect(result.current.form.getValues('email')).toBe('<EMAIL>'); }); }); describe('useContactFormConfig', () => { it('应该返回正确的配置对象', () => { const { result } = renderHook(() => useContactFormConfig()); expect(result.current).toEqual({ maxRetries: 3, retryDelay: 1000, showToast: true, }); }); it('应该返回稳定的配置对象（useMemo 测试）', () => { const { result, rerender } = renderHook(() => useContactFormConfig());

const firstConfig = result.current; rerender();

const secondConfig = result.current; expect(firstConfig).toBe(secondConfig); }); it('配置对象应该包含正确的类型', () => { const { result } = renderHook(() => useContactFormConfig()); expect(typeof result.current.maxRetries).toBe('number'); expect(typeof result.current.retryDelay).toBe('number'); expect(typeof result.current.showToast).toBe('boolean'); }); }); });
