#!/usr/bin/env node

/**
 * AI审查工具集成脚本 - 第二层验证
 * 集成 zen-mcp-server 工具，配置实用主义导向
 * 避免过度工程化建议，专注实际问题
 */
import fs from 'fs';
import path from 'path';
import { generateCompleteReport } from './ai-review-templates.js';

// 加载配置
const configPath = path.join(process.cwd(), '.ai-review-config.json');
let config = {};

if (fs.existsSync(configPath)) {
  config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
} else {
  console.warn('⚠️ AI审查配置文件不存在，使用默认配置');
  config = {
    pragmaticMode: true,
    avoidOverEngineering: true,
    reviewSettings: {
      codeReview: { enabled: true },
      securityAudit: { enabled: true },
      architectureAnalysis: { enabled: true },
    },
  };
}

/**
 * 模拟 zen-mcp-server codereview 工具调用
 * 实际实现中会调用真实的 zen-mcp-server
 */
async function executeCodeReview(files) {
  console.log('🔍 执行代码质量审查...');
  console.log(`📋 实用主义模式: ${config.pragmaticMode ? '启用' : '禁用'}`);

  // 模拟代码审查结果
  const mockResults = {
    functionalityScore: 88,
    maintainabilityScore: 85,
    findings: [
      {
        type: 'functionality',
        severity: 'medium',
        description: '函数参数验证可以更完善',
        solution: '添加基础的参数类型检查',
        effort: 'LOW',
        impact: 'MEDIUM',
        file: files[0] || 'src/lib/utils.ts',
        line: 25,
      },
      {
        type: 'maintainability',
        severity: 'low',
        description: '代码注释可以更详细',
        solution: '为复杂函数添加简要注释',
        effort: 'LOW',
        impact: 'LOW',
        file: files[0] || 'src/lib/utils.ts',
        line: 55,
      },
    ],
  };

  // 实用主义过滤：移除过度工程化建议
  if (config.avoidOverEngineering) {
    mockResults.findings = mockResults.findings.filter(
      (finding) =>
        !finding.description.includes('设计模式') &&
        !finding.description.includes('抽象层') &&
        !finding.description.includes('重构为')
    );
  }

  return mockResults;
}

/**
 * 模拟 zen-mcp-server secaudit 工具调用
 */
async function executeSecurityAudit(files) {
  console.log('🔒 执行安全审查...');

  // 模拟安全审查结果
  const mockResults = {
    securityScore: 92,
    overallRisk: 'LOW',
    vulnerabilities: [
      {
        type: 'security',
        severity: 'medium',
        description: '环境变量访问需要更安全的方式',
        solution: "使用 process.env['VARIABLE_NAME'] 语法",
        effort: 'LOW',
        impact: 'MEDIUM',
        file: files[0] || 'src/lib/utils.ts',
        line: 47,
        cwe: 'CWE-200',
      },
    ],
  };

  return mockResults;
}

/**
 * 模拟架构分析
 */
async function executeArchitectureAnalysis(files) {
  console.log('🏗️ 执行架构一致性分析...');

  // 模拟架构分析结果
  const mockResults = {
    performanceScore: 90,
    findings: [
      {
        type: 'performance',
        severity: 'low',
        description: '缓存机制运行良好',
        solution: '保持当前实现',
        effort: 'NONE',
        impact: 'POSITIVE',
        file: files[0] || 'src/lib/utils.ts',
        line: 20,
      },
    ],
  };

  return mockResults;
}

/**
 * 获取需要审查的文件列表
 */
function getFilesToReview() {
  const defaultFiles = [
    'src/lib/utils.ts',
    'src/app/page.tsx',
    'src/components/ui/button.tsx',
  ];

  // 检查文件是否存在
  return defaultFiles.filter((file) => fs.existsSync(file));
}

/**
 * 生成审查报告
 */
function generateReport(analysisResults, outputPath) {
  const report = generateCompleteReport(analysisResults, config);

  // 保存报告
  fs.writeFileSync(outputPath, JSON.stringify(report, null, 2));

  return report;
}

/**
 * 显示审查结果摘要
 */
function displaySummary(report) {
  console.log('\n📊 AI审查结果摘要');
  console.log('='.repeat(50));
  console.log(`🎯 综合评分: ${report.executiveSummary.overallScore}/100`);
  console.log(`📋 审查建议: ${report.executiveSummary.recommendation}`);
  console.log(`🔍 发现问题: ${report.executiveSummary.keyFindings.length} 个`);

  if (report.pragmaticRecommendations.recommendations.length > 0) {
    console.log('\n💡 主要建议:');
    report.pragmaticRecommendations.recommendations
      .slice(0, 3)
      .forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec.issue}`);
        console.log(`      解决方案: ${rec.pragmaticSolution}`);
      });
  }

  console.log(`\n✅ 详细报告已保存到: .ai-review-report.json`);
}

/**
 * 主执行函数
 */
async function main() {
  console.log('🤖 开始第二层AI技术审查...');
  console.log('📋 基于实用主义导向，避免过度工程化建议');

  const startTime = Date.now();
  const isSecurityOnly = process.argv.includes('--security');
  const files = getFilesToReview();

  if (files.length === 0) {
    console.warn('⚠️ 未找到需要审查的文件');
    return;
  }

  console.log(`📁 审查文件: ${files.length} 个`);

  try {
    let analysisResults = {};

    if (isSecurityOnly) {
      // 仅执行安全审查
      analysisResults.securityAudit = await executeSecurityAudit(files);
      analysisResults.codeAnalysis = {
        functionalityScore: 0,
        maintainabilityScore: 0,
      };
      analysisResults.performanceAnalysis = { performanceScore: 0 };
    } else {
      // 执行完整审查
      analysisResults.codeAnalysis = await executeCodeReview(files);
      analysisResults.securityAudit = await executeSecurityAudit(files);
      analysisResults.performanceAnalysis =
        await executeArchitectureAnalysis(files);
    }

    // 生成报告
    const report = generateReport(analysisResults, '.ai-review-report.json');

    // 显示摘要
    displaySummary(report);

    const totalTime = Date.now() - startTime;
    console.log(`\n⏱️ 审查耗时: ${totalTime}ms`);

    // 返回结果供其他脚本使用
    return {
      success: true,
      score: report.executiveSummary.overallScore,
      recommendation: report.executiveSummary.recommendation,
      reportPath: '.ai-review-report.json',
      totalTime,
    };
  } catch (error) {
    console.error('❌ AI审查执行失败:', error.message);
    return {
      success: false,
      error: error.message,
    };
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error('💥 AI审查脚本执行失败:', error);
    process.exit(1);
  });
}

export {
  main,
  executeCodeReview,
  executeSecurityAudit,
  executeArchitectureAnalysis,
};
