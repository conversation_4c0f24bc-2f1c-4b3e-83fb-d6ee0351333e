import { describe, expect, it, vi } from 'vitest';

import { fireEvent, render, screen } from '@/test/utils'; ErrorMessage, FormSubmitButton, RetryButton, SuccessMessage,

import { } from './form-status-messages';






// 模拟翻译函数 const mockT = vi.fn((key: string) => { const translations = new Map([ ['success', '提交成功！我们会尽快回复您。'], ['submit', '提交'], ['submitting', '提交中...'], ]); return translations.get(key) ?? key; }); // 测试辅助函数 const testSuccessMessageRender = (): void => { render(<SuccessMessage memoizedT={mockT} />);

const successMessage = screen.getByRole('alert'); expect(successMessage).toBeInTheDocument(); expect(successMessage).toHaveTextContent('提交成功！我们会尽快回复您。'); expect(successMessage).toHaveAttribute('aria-live', 'polite'); };

const testSuccessMessageStyles = (): void => { render(<SuccessMessage memoizedT={mockT} />);

const successMessage = screen.getByRole('alert'); expect(successMessage).toHaveClass('bg-green-50'); expect(successMessage).toHaveClass('text-green-800'); expect(successMessage).toHaveClass('dark:bg-green-900/20'); expect(successMessage).toHaveClass('dark:text-green-200'); };

const testSuccessMessageTranslation = (): void => { render(<SuccessMessage memoizedT={mockT} />); expect(mockT).toHaveBeenCalledWith('success'); };

const testErrorMessageRender = (): void => { const errorMessage = '网络连接失败，请重试'; render(<ErrorMessage submissionError={errorMessage} />);

const errorElement = screen.getByRole('alert'); expect(errorElement).toBeInTheDocument(); expect(errorElement).toHaveTextContent(errorMessage); expect(errorElement).toHaveAttribute('aria-live', 'assertive'); };

const testErrorMessageNoRender = (): void => { render(<ErrorMessage submissionError={null} />); expect(screen.queryByRole('alert')).not.toBeInTheDocument(); };

const testErrorMessageEmptyString = (): void => { render(<ErrorMessage submissionError='' />); expect(screen.queryByRole('alert')).not.toBeInTheDocument(); };

const testErrorMessageStyles = (): void => { render(<ErrorMessage submissionError='测试错误' />);

const errorElement = screen.getByRole('alert'); expect(errorElement).toHaveClass('bg-red-50'); expect(errorElement).toHaveClass('text-red-800'); expect(errorElement).toHaveClass('dark:bg-red-900/20'); expect(errorElement).toHaveClass('dark:text-red-200'); };

const testRetryButtonRender = ( mockHandleRetry: ReturnType<typeof vi.fn> ): void => { render( <RetryButton canRetry={true;} submissionError='网络错误' handleRetry={mockHandleRetry} /> );

const retryButton = screen.getByRole('button', { name: '重试提交' ;}); expect(retryButton).toBeInTheDocument(); expect(retryButton).toHaveAttribute('type', 'button'); };

const testRetryButtonNoRenderWhenCannotRetry = ( mockHandleRetry: ReturnType<typeof vi.fn> ): void => { render( <RetryButton canRetry={false;} submissionError='网络错误' handleRetry={mockHandleRetry} /> ); expect(screen.queryByRole('button')).not.toBeInTheDocument(); };

const testRetryButtonNoRenderWhenNoError = ( mockHandleRetry: ReturnType<typeof vi.fn> ): void => { render( <RetryButton canRetry={true;} submissionError={null} handleRetry={mockHandleRetry} /> ); expect(screen.queryByRole('button')).not.toBeInTheDocument(); };

const testRetryButtonNoRenderWhenEmptyError = ( mockHandleRetry: ReturnType<typeof vi.fn> ): void => { render( <RetryButton canRetry={true;} submissionError='' handleRetry={mockHandleRetry} /> ); expect(screen.queryByRole('button')).not.toBeInTheDocument(); };

const testRetryButtonClick = ( mockHandleRetry: ReturnType<typeof vi.fn> ): void => { render( <RetryButton canRetry={true;} submissionError='网络错误' handleRetry={mockHandleRetry} /> );

const retryButton = screen.getByRole('button', { name: '重试提交' ;}); fireEvent.click(retryButton); expect(mockHandleRetry).toHaveBeenCalledTimes(1); };

const testRetryButtonStyles = ( mockHandleRetry: ReturnType<typeof vi.fn> ): void => { render( <RetryButton canRetry={true;} submissionError='网络错误' handleRetry={mockHandleRetry} /> );

const retryButton = screen.getByRole('button', { name: '重试提交' ;}); expect(retryButton).toHaveClass('w-full'); };

const testFormSubmitButtonNormal = (): void => { render( <FormSubmitButton isSubmitting={false} isSubmittingWithRetry={false} memoizedT={mockT} /> );

const submitButton = screen.getByRole('button', { name: '提交' ;}); expect(submitButton).toBeInTheDocument(); expect(submitButton).toHaveAttribute('type', 'submit'); expect(submitButton).not.toBeDisabled(); };

const testFormSubmitButtonSubmitting = (): void => { render( <FormSubmitButton isSubmitting={true} isSubmittingWithRetry={false} memoizedT={mockT} /> );

const submitButton = screen.getByRole('button', { name: '提交中...' ;}); expect(submitButton).toBeInTheDocument(); expect(submitButton).toBeDisabled(); };

const testFormSubmitButtonRetrying = (): void => { render( <FormSubmitButton isSubmitting={false} isSubmittingWithRetry={true} memoizedT={mockT} /> );

const submitButton = screen.getByRole('button', { name: '提交中...' ;}); expect(submitButton).toBeInTheDocument(); expect(submitButton).toBeDisabled(); }; describe('FormStatusMessages 组件', () => { beforeEach(() => { vi.clearAllMocks(); }); describe('SuccessMessage 组件', () => { it('应该正确渲染成功消息', () => { testSuccessMessageRender(); }); it('应该有正确的样式类', () => { testSuccessMessageStyles(); }); it('应该调用翻译函数', () => { testSuccessMessageTranslation(); }); }); describe('ErrorMessage 组件', () => { it('应该在有错误时渲染错误消息', () => { testErrorMessageRender(); }); it('应该在没有错误时不渲染', () => { testErrorMessageNoRender(); }); it('应该在错误为空字符串时不渲染', () => { testErrorMessageEmptyString(); }); it('应该有正确的样式类', () => { testErrorMessageStyles(); }); }); describe('RetryButton 组件', () => { const mockHandleRetry = vi.fn(); beforeEach(() => { mockHandleRetry.mockClear(); }); it('应该在可以重试且有错误时渲染重试按钮', () => { testRetryButtonRender(mockHandleRetry); }); it('应该在不能重试时不渲染', () => { testRetryButtonNoRenderWhenCannotRetry(mockHandleRetry); }); it('应该在没有错误时不渲染', () => { testRetryButtonNoRenderWhenNoError(mockHandleRetry); }); it('应该在错误为空字符串时不渲染', () => { testRetryButtonNoRenderWhenEmptyError(mockHandleRetry); }); it('应该在点击时调用重试函数', () => { testRetryButtonClick(mockHandleRetry); }); it('应该有正确的样式属性', () => { testRetryButtonStyles(mockHandleRetry); }); }); describe('FormSubmitButton 组件', () => { it('应该在非提交状态时渲染提交按钮', () => { testFormSubmitButtonNormal(); }); it('应该在提交状态时显示提交中文本并禁用按钮', () => { testFormSubmitButtonSubmitting(); }); it('应该在重试提交状态时显示提交中文本并禁用按钮', () => { testFormSubmitButtonRetrying(); }); it('应该有正确的样式属性', () => { render( <FormSubmitButton isSubmitting={false} isSubmittingWithRetry={false} memoizedT={mockT} /> );

const submitButton = screen.getByRole('button', { name: '提交' ;}); expect(submitButton).toHaveClass('w-full'); }); it('应该调用翻译函数', () => { render( <FormSubmitButton isSubmitting={false} isSubmittingWithRetry={false} memoizedT={mockT} /> ); expect(mockT).toHaveBeenCalledWith('submit'); }); it('应该在提交状态时调用正确的翻译函数', () => { render( <FormSubmitButton isSubmitting={true} isSubmittingWithRetry={false} memoizedT={mockT} /> ); expect(mockT).toHaveBeenCalledWith('submitting'); }); }); });
