#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * 高级不安全错误类型修复脚本
 */

function fixUnsafeErrorTypesAdvanced(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 1. 修复 catch 块中的错误处理
    const catchBlockPatterns = [
      // catch (error) { console.log(error.message) }
      {
        pattern: /catch\s*\(\s*(\w+)\s*\)\s*{([^}]*)\1\.message([^}]*)}/gs,
        replacement: (match, errorVar, before, after) => {
          const safeAccess = `${errorVar} instanceof Error ? ${errorVar}.message : String(${errorVar})`;
          return `catch (${errorVar}) {${before}${safeAccess}${after}}`;
        }
      },
      
      // catch (error) { console.error(error) }
      {
        pattern: /catch\s*\(\s*(\w+)\s*\)\s*{([^}]*console\.(log|error|warn)\s*\(\s*)\1(\s*\)[^}]*)}/gs,
        replacement: (match, errorVar, before, method, after) => {
          const safeAccess = `${errorVar} instanceof Error ? ${errorVar}.message : String(${errorVar})`;
          return `catch (${errorVar}) {${before}${safeAccess}${after}}`;
        }
      }
    ];
    
    // 2. 修复错误赋值模式
    const assignmentPatterns = [
      // const message = error.message
      {
        pattern: /const\s+(\w+)\s*=\s*(\w+)\.message/g,
        replacement: 'const $1 = $2 instanceof Error ? $2.message : String($2)'
      },
      
      // let message = error.message
      {
        pattern: /let\s+(\w+)\s*=\s*(\w+)\.message/g,
        replacement: 'let $1 = $2 instanceof Error ? $2.message : String($2)'
      },
      
      // variable = error.message
      {
        pattern: /(\w+)\s*=\s*(\w+)\.message(?!\s*\?)/g,
        replacement: '$1 = $2 instanceof Error ? $2.message : String($2)'
      },
      
      // const stack = error.stack
      {
        pattern: /const\s+(\w+)\s*=\s*(\w+)\.stack/g,
        replacement: 'const $1 = $2 instanceof Error ? $2.stack : undefined'
      },
      
      // const name = error.name
      {
        pattern: /const\s+(\w+)\s*=\s*(\w+)\.name/g,
        replacement: 'const $1 = $2 instanceof Error ? $2.name : "Error"'
      }
    ];
    
    // 3. 修复函数调用模式
    const callPatterns = [
      // error.toString()
      {
        pattern: /(\w+)\.toString\(\)/g,
        replacement: '($1 instanceof Error ? $1.toString() : String($1))'
      },
      
      // error.valueOf()
      {
        pattern: /(\w+)\.valueOf\(\)/g,
        replacement: '($1 instanceof Error ? $1.valueOf() : $1)'
      }
    ];
    
    // 4. 修复模板字符串中的错误访问
    const templatePatterns = [
      // `Error: ${error.message}`
      {
        pattern: /`([^`]*)\$\{(\w+)\.message\}([^`]*)`/g,
        replacement: '`$1${$2 instanceof Error ? $2.message : String($2)}$3`'
      },
      
      // `${error}`
      {
        pattern: /`([^`]*)\$\{(\w+)\}([^`]*)`/g,
        replacement: (match, before, errorVar, after) => {
          // 只替换看起来像错误变量的情况
          if (errorVar.toLowerCase().includes('error') || errorVar.toLowerCase().includes('err')) {
            return `\`${before}\${${errorVar} instanceof Error ? ${errorVar}.message : String(${errorVar})}\${after}\``;
          }
          return match;
        }
      }
    ];
    
    // 5. 修复条件表达式中的错误访问
    const conditionalPatterns = [
      // error ? error.message : 'Unknown'
      {
        pattern: /(\w+)\s*\?\s*\1\.message\s*:/g,
        replacement: '$1 instanceof Error ? $1.message :'
      },
      
      // error && error.message
      {
        pattern: /(\w+)\s*&&\s*\1\.message/g,
        replacement: '$1 instanceof Error && $1.message'
      }
    ];
    
    // 应用所有修复模式
    const allPatterns = [
      ...assignmentPatterns,
      ...callPatterns,
      ...templatePatterns,
      ...conditionalPatterns
    ];
    
    for (const { pattern, replacement } of allPatterns) {
      const newContent = content.replace(pattern, replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    }
    
    // 应用 catch 块修复（需要特殊处理）
    for (const { pattern, replacement } of catchBlockPatterns) {
      const newContent = content.replace(pattern, replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    }
    
    // 6. 添加类型保护函数（如果还没有）
    if (content.includes('error') && content.includes('catch') && !content.includes('function isError')) {
      const hasImports = content.includes('import ');
      const insertPosition = hasImports ? 
        content.indexOf('\n', content.lastIndexOf('import ')) + 1 : 0;
      
      const typeGuards = `
/**
 * 类型保护：检查是否为 Error 实例
 */
function isError(error: unknown): error is Error {
  return error instanceof Error;
}

/**
 * 安全获取错误消息
 */
function getErrorMessage(error: unknown): string {
  return error instanceof Error ? error.message : String(error);
}

/**
 * 安全获取错误堆栈
 */
function getErrorStack(error: unknown): string | undefined {
  return error instanceof Error ? error.stack : undefined;
}

`;
      
      content = content.slice(0, insertPosition) + typeGuards + content.slice(insertPosition);
      modified = true;
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 修复高级不安全错误类型: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🚀 开始高级不安全错误类型修复...\n');
  
  // 获取有不安全错误类型的文件
  let errorFiles = [];
  try {
    const lintOutput = execSync('pnpm lint 2>&1 | grep -B 1 "no-unsafe-"', { encoding: 'utf8' });
    const lines = lintOutput.split('\n').filter(line => line.trim());
    
    for (const line of lines) {
      if (line.includes('.ts') || line.includes('.tsx')) {
        const match = line.match(/^([^:]+):/);
        if (match) {
          const filePath = match[1].trim();
          if (filePath.startsWith('./')) {
            errorFiles.push(filePath.substring(2));
          } else {
            errorFiles.push(filePath);
          }
        }
      }
    }
  } catch (error) {
    console.log('无法获取不安全错误类型文件列表，将处理所有文件');
  }
  
  // 去重
  errorFiles = [...new Set(errorFiles)];
  
  if (errorFiles.length === 0) {
    console.log('未找到有不安全错误类型的文件，处理所有 TypeScript 文件');
    // 如果没有找到特定文件，处理所有文件
    const extensions = ['.ts', '.tsx'];
    const directories = ['src/components', 'src/hooks', 'src/app', 'src/lib'];
    
    for (const dir of directories) {
      if (!fs.existsSync(dir)) continue;
      
      function processDirectory(dirPath) {
        const items = fs.readdirSync(dirPath);
        
        for (const item of items) {
          const fullPath = path.join(dirPath, item);
          const stat = fs.statSync(fullPath);
          
          if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
            processDirectory(fullPath);
          } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
            errorFiles.push(fullPath);
          }
        }
      }
      
      processDirectory(dir);
    }
  }
  
  let totalFixed = 0;
  
  for (const filePath of errorFiles) {
    if (fs.existsSync(filePath) && fixUnsafeErrorTypesAdvanced(filePath)) {
      totalFixed++;
    }
  }
  
  console.log(`\n✅ 完成！共修复了 ${totalFixed} 个文件的高级不安全错误类型`);
  
  // 运行 ESLint 检查结果
  console.log('\n🔍 检查修复结果...');
  try {
    const result1 = execSync('pnpm lint 2>&1 | grep -c "no-unsafe-assignment"', { encoding: 'utf8' });
    console.log(`剩余不安全赋值错误: ${result1.trim()}`);
    
    const result2 = execSync('pnpm lint 2>&1 | grep -c "no-unsafe-call"', { encoding: 'utf8' });
    console.log(`剩余不安全调用错误: ${result2.trim()}`);
  } catch (error) {
    console.log('检查完成');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixUnsafeErrorTypesAdvanced };
