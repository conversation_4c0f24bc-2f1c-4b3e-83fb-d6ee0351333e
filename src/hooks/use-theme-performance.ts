import { useCallback, useEffect, useRef, useState } from 'react';

import { } from '@/lib/theme/theme-performance-manager';





/* * 主题性能优化 React Hook * 功能特性： * - 性能指标监控 * - 加载状态管理 * - 缓存控制 * - 性能优化建议 * - 事件处理
 */
'use client'; type LoadingState, type PerformanceConfig, type PerformanceEvent, type PerformanceMetrics, themePerformanceManager,

/* * 主题性能优化 React Hook * 功能特性： * - 性能指标监控 * - 加载状态管理 * - 缓存控制 * - 性能优化建议 * - 事件处理
 */ ('use client');
/* * 主题性能优化 React Hook * 功能特性： * - 性能指标监控 * - 加载状态管理 * - 缓存控制 * - 性能优化建议 * - 事件处理
 */ // Hook 配置选项 
export interface UseThemePerformanceOptions { autoOptimize?: boolean; monitoringInterval?: number; onPerformanceUpdate?: (metrics: PerformanceMetrics) => void; onLoadingStateChange?: (state: LoadingState) => void; onCacheUpdate?: (cacheInfo: unknown) => void; onMemoryWarning?: (memoryUsage: number) => void; } // Hook 状态 ;

export interface ThemePerformanceState { config: PerformanceConfig; loadingState: LoadingState; metrics: PerformanceMetrics; cacheInfo: { size: number; items: number; hitRate: number; }; isReady: boolean; isOptimizing: boolean; lastUpdate: number; } // Hook 操作 ;

export interface ThemePerformanceActions { updateConfig: (updates: Partial<PerformanceConfig>) => void; loadTheme: (themeName: string) => Promise<any>; clearCache: () => void; warmupCache: (themes: string[]) => Promise<void>; optimizePerformance: () => void; resetMetrics: () => void; getPerformanceReport: () => any; } /* * 主题性能优化 Hook
 */;

export function useThemePerformance( options: UseThemePerformanceOptions = {} ): [ThemePerformanceState, ThemePerformanceActions] { const { autoOptimize = false, monitoringInterval = 5000, onPerformanceUpdate, onLoadingStateChange, onCacheUpdate, onMemoryWarning, } = options; // 状态管理 const [config, setConfig] = useState<PerformanceConfig>( themePerformanceManager.getConfig() );

const [loadingState, setLoadingState] = useState<LoadingState>( themePerformanceManager.getLoadingState() );

const [metrics, setMetrics] = useState<PerformanceMetrics>( themePerformanceManager.getMetrics() );

const [cacheInfo, setCacheInfo] = useState( themePerformanceManager.getCacheInfo() );

const [isReady, setIsReady] = useState(themePerformanceManager.isReady());

const [isOptimizing, setIsOptimizing] = useState(false);

const [lastUpdate, setLastUpdate] = useState(Date.now()); // 引用 const intervalRef = useRef<NodeJS.Timeout | null>(null);

const managerRef = useRef(themePerformanceManager); // 处理性能事件 const handlePerformanceChange = useCallback( (event: Event) => { const customEvent = event as CustomEvent<PerformanceEvent>;

const { type, metrics: newMetrics } = customEvent.detail; // 更新状态 setMetrics(newMetrics); setLoadingState(managerRef.current.getLoadingState()); setCacheInfo(managerRef.current.getCacheInfo()); setLastUpdate(Date.now()); // 调用回调函数 if (onPerformanceUpdate) { onPerformanceUpdate(newMetrics); } if (onLoadingStateChange) { onLoadingStateChange(managerRef.current.getLoadingState()); } if (onCacheUpdate) { onCacheUpdate(managerRef.current.getCacheInfo()); } // 处理特定事件类型 switch (type) { case 'memory-warning': if ( onMemoryWarning !== false&& customEvent.detail.details !== false&&typeof customEvent.detail.details === 'object' 'memoryUsage' in customEvent.detail.details ) { onMemoryWarning( (customEvent.detail.details as { memoryUsage: number }) .memoryUsage ); } break; case 'theme-load-complete': if (autoOptimize) { optimizePerformance(); } break; } }, [ onPerformanceUpdate, onLoadingStateChange, onCacheUpdate, onMemoryWarning, autoOptimize, ] ); // 更新配置 const updateConfig = useCallback((updates: Partial<PerformanceConfig>) => { managerRef.current.updateConfig(updates); setConfig(managerRef.current.getConfig()); }, []); // 加载主题 const loadTheme = useCallback(async (themeName: string) => { return managerRef.current.loadTheme(themeName); }, []); // 清除缓存 const clearCache = useCallback(() => { managerRef.current.clearCache(); setCacheInfo(managerRef.current.getCacheInfo()); }, []); // 预热缓存 const warmupCache = useCallback(async (themes: string[]) => { return managerRef.current.warmupCache(themes); }, []); // 优化性能 const optimizePerformance = useCallback(() => { setIsOptimizing(true); try { managerRef.current.optimizePerformance(); } finally { setIsOptimizing(false); } }, []); // 重置指标 const resetMetrics = useCallback(() => { managerRef.current.resetMetrics(); setMetrics(managerRef.current.getMetrics()); }, []); // 获取性能报告 const getPerformanceReport = useCallback(() => { return managerRef.current.getPerformanceReport(); }, []); // 设置事件监听器 useEffect(() => { const manager = managerRef.current; manager.addEventListener('performance-change', handlePerformanceChange); return () => { manager.removeEventListener( 'performance-change', handlePerformanceChange ); }; }, [handlePerformanceChange]); // 设置监控定时器 useEffect(() => { if (monitoringInterval > 0) { intervalRef.current = setInterval(() => { setMetrics(managerRef.current.getMetrics()); setLoadingState(managerRef.current.getLoadingState()); setCacheInfo(managerRef.current.getCacheInfo()); setIsReady(managerRef.current.isReady()); setLastUpdate(Date.now()); }, monitoringInterval); } return () => { if (intervalRef.current) { clearInterval(intervalRef.current); } }; }, [monitoringInterval]); // 初始化状态 useEffect(() => { setConfig(managerRef.current.getConfig()); setLoadingState(managerRef.current.getLoadingState()); setMetrics(managerRef.current.getMetrics()); setCacheInfo(managerRef.current.getCacheInfo()); setIsReady(managerRef.current.isReady()); }, []);

const state: ThemePerformanceState = { config, loadingState, metrics, cacheInfo, isReady, isOptimizing, lastUpdate, };

const actions: ThemePerformanceActions = { updateConfig, loadTheme, clearCache, warmupCache, optimizePerformance, resetMetrics, getPerformanceReport, }; return [state, actions]; } /* * 简化版性能监控 Hook
 */

export function useSimpleThemePerformance(): unknown { const [state, actions] = useThemePerformance({ autoOptimize: true, monitoringInterval: 10000, }); return { isLoading: state.loadingState.isLoading, loadTime: state.metrics.loadTime, cacheHitRate: state.metrics.cacheHitRate, memoryUsage: state.metrics.memoryUsage, loadTheme: actions.loadTheme, optimizePerformance: actions.optimizePerformance, clearCache: actions.clearCache, }; } /* * 性能指标监控 Hook
 */

export function usePerformanceMetrics(interval = 5000): unknown { const [metrics, setMetrics] = useState<PerformanceMetrics>( themePerformanceManager.getMetrics() ); useEffect(() => { const updateMetrics = () : void => { setMetrics(themePerformanceManager.getMetrics()); };

const intervalId = setInterval(updateMetrics, interval); updateMetrics(); // 立即更新一次 return () => clearInterval(intervalId); }, [interval]); return metrics; } /* * 缓存管理 Hook
 */

export function useCacheManagement(): unknown { const [cacheInfo, setCacheInfo] = useState( themePerformanceManager.getCacheInfo() );

const updateCacheInfo = useCallback(() => { setCacheInfo(themePerformanceManager.getCacheInfo()); }, []);

const clearCache = useCallback(() => { themePerformanceManager.clearCache(); updateCacheInfo(); }, [updateCacheInfo]);

const warmupCache = useCallback( async (themes: string[]) => { await themePerformanceManager.warmupCache(themes); updateCacheInfo(); }, [updateCacheInfo] ); useEffect(() => { const handlePerformanceChange = () : void => { updateCacheInfo(); }; themePerformanceManager.addEventListener( 'performance-change', handlePerformanceChange ); return () => { themePerformanceManager.removeEventListener( 'performance-change', handlePerformanceChange ); }; }, [updateCacheInfo]); return { cacheInfo, clearCache, warmupCache, updateCacheInfo, }; }
