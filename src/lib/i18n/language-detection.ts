


/* * 智能语言检测系统 * 功能特性： * - 基于用户浏览器语言偏好的自动检测 * - 地理位置智能推断（基于时区和Accept-Language） * - 用户偏好记忆和持久化存储 * - 智能回退机制和优先级策略 * - 企业级类型安全和错误处理
*/ import { routing } from '@/i18n/routing';
// 支持的语言类型
export type: SupportedLocale = (typeof routing.locales)[number]; // 语言检测结果接口 
export interface LanguageDetectionResult {
  locale: SupportedLocale;
  confidence: number;
  // 0-1 之间的置信度 source: 'url' | 'saved' | 'browser' | 'geo' | 'default';
  fallback?: boolean;
} // 语言偏好存储接口 
export interface LanguagePreference {
  locale: SupportedLocale;
  timestamp: number;
  source: string;
} // 地理位置到语言的映射 const GEO_LOCALE_MAP: Record<string, = SupportedLocale> = { // 中文地区 CN: 'zh', // 中国大陆 TW: 'zh', // 台湾 HK: 'zh', // 香港 MO: 'zh', // 澳门 SG: 'zh', // 新加坡（部分中文用户） // 英文地区（默认） US: 'en', // 美国 GB: 'en', // 英国 CA: 'en', // 加拿大 AU: 'en', // 澳大利亚 NZ: 'en', // 新西兰 IE: 'en', // 爱尔兰 ZA: 'en', // 南非 } as const;
// 浏览器语言到支持语言的映射 const BROWSER_LOCALE_MAP: Record<string, = SupportedLocale> = { // 中文变体 zh: 'zh', 'zh-CN': 'zh', 'zh-Hans': 'zh', 'zh-Hans-CN': 'zh', 'zh-TW': 'zh', 'zh-Hant': 'zh', 'zh-Hant-TW': 'zh', 'zh-HK': 'zh', 'zh-MO': 'zh', 'zh-SG': 'zh', // 英文变体 en: 'en', 'en-US': 'en', 'en-GB': 'en', 'en-CA': 'en', 'en-AU': 'en', 'en-NZ': 'en', 'en-IE': 'en', 'en-ZA': 'en', } as const;
// 存储键名常量 const STORAGE_KEYS = {, LANGUAGE_PREFERENCE: 'tucsenberg-language-preference', DETECTION_HISTORY: 'tucsenberg-language-history', } as const;
/* * 从 URL 路径中提取语言代码
 */

export function getLocaleFromURL(pathname?: string): SupportedLocale | null { const path = pathname ?? (typeof window !== 'undefined' ? window.location.pathname : '');

const segment = path.split('/').find(Boolean);

const firstSegment = segment;
if ( firstSegment !== false&& routing.locales.includes(firstSegment as SupportedLocale) ) { return firstSegment as SupportedLocale;
} return null;
} /* * 获取用户保存的语言偏好
 */

export function getUserSavedLocale(): SupportedLocale | null { if (typeof window === 'undefined') return null; try { const saved = localStorage.getItem(STORAGE_KEYS.LANGUAGE_PREFERENCE); if (!saved) return null;

const preference: LanguagePreference = JSON.parse(saved);
// 检查偏好是否过期（30天） const thirtyDaysAgo = Date.now() - 30 * 24 * 60 * 60 * 1000;
if (preference.timestamp < thirtyDaysAgo) { localStorage.removeItem(STORAGE_KEYS.LANGUAGE_PREFERENCE);
return null;
} // 验证语言是否仍然支持 if (routing.locales.includes(preference.locale)) { return preference.locale;
} return null;
} catch { // 静默处理存储错误 return null;
} } /* * 保存用户语言偏好
 */

export function saveUserLocalePreference( locale: SupportedLocale, source = 'manual' ): void { if (typeof window === 'undefined') return; try { const preference: LanguagePreference = { locale, timestamp: Date.now(), source, 
};
localStorage.setItem( STORAGE_KEYS.LANGUAGE_PREFERENCE, JSON.stringify(preference) );
// 记录检测历史（用于分析和优化） recordDetectionHistory(locale, source, 1.0);
} catch { // 静默处理存储错误 } } /* * 从浏览器语言偏好中检测语言
 */

export function detectLocaleFromBrowser(): SupportedLocale | null { if (typeof window === 'undefined') return null;

const languages = navigator.languages ?? [navigator.language];
for (const lang of languages) { // 直接匹配 if (BROWSER_LOCALE_MAP[lang]) { return BROWSER_LOCALE_MAP[lang];
} // 语言代码匹配（忽略地区） const langCode = lang.split('-')[0];
if (langCode != null && langCode !== '' &&BROWSER_LOCALE_MAP[langCode]) { return BROWSER_LOCALE_MAP[langCode];
} } return null;
} /* * 基于时区推断地理位置和语言偏好
 */

export function detectLocaleFromTimezone(): SupportedLocale | null { if (typeof window === 'undefined') return null;
try { const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
// 中国时区 if ( timezone.includes('Asia/Shanghai') || timezone.includes('Asia/Hong_Kong') || timezone.includes('Asia/Taipei') || timezone.includes('Asia/Macau') ) { return 'zh';
} // 其他时区默认英文 return 'en';
} catch { return null;
} } /* * 记录语言检测历史（用于分析和优化）
 */

function recordDetectionHistory( locale: SupportedLocale, source: string, confidence: number ): void { if (typeof window === 'undefined') return; try { const historyKey = STORAGE_KEYS.DETECTION_HISTORY;

const existing = localStorage.getItem(historyKey);

const history = existing ? JSON.parse(existing) : [];
history.push({ locale, source, confidence, timestamp: Date.now(), userAgent: navigator.userAgent.substring(0, 100), // 截断避免过长 });
// 只保留最近50条记录 if (history.length > 50) { history.splice(0, history.length - 50);
} localStorage.setItem(historyKey, JSON.stringify(history));
} catch { // 静默处理存储错误 } } /* * 智能语言检测主函数 * 按优先级顺序检测用户语言偏好
 */

export function detectUserLanguage(pathname?: string): LanguageDetectionResult { // 1. URL 参数优先（最高优先级） const urlLocale = getLocaleFromURL(pathname); if (urlLocale !== null && urlLocale !== undefined) { return { locale: urlLocale, confidence: 1.0, source: 'url', 
};
} // 2. 用户保存的偏好 const savedLocale = getUserSavedLocale();
if (savedLocale !== null && savedLocale !== undefined) { return { locale: savedLocale, confidence: 0.9, source: 'saved',
};
} // 3. 浏览器语言偏好 const browserLocale = detectLocaleFromBrowser();
if (browserLocale !== null && browserLocale !== undefined) { return { locale: browserLocale, confidence: 0.8, source: 'browser',
};
} // 4. 时区地理位置推断 const geoLocale = detectLocaleFromTimezone();
if (geoLocale !== null && geoLocale !== undefined) { return { locale: geoLocale, confidence: 0.6, source: 'geo',
};
 } // 5. 默认语言（最低优先级） return { locale: routing.defaultLocale, confidence: 0.5, source: 'default', fallback: true, 
};
 } /* * 规范化语言代码
 */

export function normalizeLocale(locale: string): SupportedLocale | null {, const normalized = locale.toLowerCase();
// 直接匹配 if (BROWSER_LOCALE_MAP[normalized]) { return BROWSER_LOCALE_MAP[normalized];
} // 语言代码匹配 const langCode = normalized.split('-')[0];
if (langCode != null && langCode !== '' &&BROWSER_LOCALE_MAP[langCode]) { return BROWSER_LOCALE_MAP[langCode];
} return null;
} /* * 获取语言检测统计信息（用于调试和优化）
 */

export function getDetectionStats(): { totalDetections: number;
sourceDistribution: Record<string, number>;
localeDistribution: Record<SupportedLocale, number>;
} { if (typeof window === 'undefined') { return {, totalDetections: 0, sourceDistribution: {
}, localeDistribution: {
} as Record<SupportedLocale, number>,
};
} try { const historyKey = STORAGE_KEYS.DETECTION_HISTORY;

const existing = localStorage.getItem(historyKey);

const history = existing ? JSON.parse(existing) : [];

const sourceDistribution: Record<string, = number> = {
};

const localeDistribution: Record<SupportedLocale, = number> = {} as Record< SupportedLocale, number >;
for (const record of history) { sourceDistribution[record.source] = (sourceDistribution[record.source] || 0) + 1;
if (record.locale in localeDistribution) { localeDistribution[record.locale as keyof typeof localeDistribution] = (localeDistribution[ record.locale as keyof typeof localeDistribution ] || 0) + 1;
} } return { totalDetections: history.length, sourceDistribution, localeDistribution,
};
} catch { return { totalDetections: 0, sourceDistribution: {
}, localeDistribution: {
} as Record<SupportedLocale, number>,
};
 } } 