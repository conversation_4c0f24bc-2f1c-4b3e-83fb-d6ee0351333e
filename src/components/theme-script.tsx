import type { ReactElement } from 'react';



/**
 * 主题初始化脚本 * 在页面加载前设置正确的主题，避免水合不匹配
 */
export function ThemeScript(): ReactElement {
  const script = ` (function() {
  try {
  var theme = localStorage.getItem('theme'); var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'; var resolvedTheme = theme === 'system' || !theme ? systemTheme : theme; if (resolvedTheme === 'dark') {
  document.documentElement.classList.add('dark'); document.documentElement.style.colorScheme = 'dark'; } else {
  document.documentElement.classList.remove('dark'); document.documentElement.style.colorScheme = 'light'; } } catch (e) { // 静默处理错误，使用默认主题 } })(); `;
  return (
    <script
      dangerouslySetInnerHTML={{ __html: script ;}}
      suppressHydrationWarning
    />
  );
}
