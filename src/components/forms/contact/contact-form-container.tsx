import { type ReactElement, memo } from 'react';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Form } from '@/components/ui/form';

import { ContactFormFields } from './contact-form-fields';
import { useContactFormLogic } from './contact-form-hooks';
import { type ContactFormContainerProps } from './contact-form-types';
import {
  ErrorMessage,
  FormSubmitButton,
  RetryButton,
  SuccessMessage,
} from './form-status-messages';

/**
 * 表单容器组件
 * 负责表单的整体布局和状态管理
 */
export const ContactFormContainer = memo(
  ({ memoizedT }: ContactFormContainerProps): ReactElement => {
    const {
      form,
      submitStatus,
      isSubmitting,
      submissionError,
      canRetry,
      onSubmit,
      handleRetry,
    } = useContactFormLogic();

    const isSubmittingWithRetry = isSubmitting;

    return (
      <div className="mx-auto max-w-2xl">
        <Card>
          <CardHeader className="text-center">
            <CardTitle className="text-2xl font-bold">
              {memoizedT('title')}
            </CardTitle>
            <CardDescription className="text-lg">
              {memoizedT('subtitle')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form
                onSubmit={(e) => {
                  void form.handleSubmit(onSubmit)(e);
                }}
                className="space-y-6"
                noValidate
                role="form"
                aria-label="联系表单"
              >
                <ContactFormFields form={form} t={memoizedT} />

                {/* 提交按钮区域 */}
                <div className="space-y-3 pt-4">
                  <FormSubmitButton
                    isSubmitting={isSubmitting}
                    isSubmittingWithRetry={isSubmittingWithRetry}
                    memoizedT={memoizedT}
                  />
                  <RetryButton
                    canRetry={canRetry}
                    submissionError={submissionError}
                    handleRetry={handleRetry}
                  />
                </div>

                {/* 状态消息区域 */}
                {submitStatus === 'success' && (
                  <SuccessMessage memoizedT={memoizedT} />
                )}
                <ErrorMessage submissionError={submissionError} />

                {/* 传统错误状态（保持向后兼容） */}
                {submitStatus === 'error' && submissionError === false && (
                  <div className="text-red-500 text-sm">
                    {memoizedT('error')}
                  </div>
                )}
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    );
  }
);

ContactFormContainer.displayName = 'ContactFormContainer';
