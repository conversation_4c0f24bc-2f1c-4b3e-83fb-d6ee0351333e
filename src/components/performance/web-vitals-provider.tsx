




/* * Web Vitals Provider 组件 * 在Next.js应用中集成性能监控
 */ 'use client'; useEffect, useRef } from 'react'; type WebVitalMetric, initWebVitals } from '@/lib/web-vitals'; /* * Web Vitals Provider 组件 * 在Next.js应用中集成性能监控
 */ /* * Web Vitals Provider 组件 * 在Next.js应用中集成性能监控
 */

interface WebVitalsProviderProps { children: React.ReactNode; /* * 是否在开发环境中启用性能监控 * @default true
 */ enableInDevelopment?: boolean; /* * 自定义性能指标处理函数
 */ onMetric?: (metric: WebVitalMetric) => void; /* * 是否显示性能调试信息 * @default false
 */ debug?: boolean; } /* * 性能调试面板组件
 */

function PerformanceDebugPanel({ metrics, }: { metrics: WebVitalMetric[]; }): React.JSX.Element | null { if (process.env.NODE_ENV !== 'development') { return null; } return ( <div style={{ position: 'fixed', top: '10px', right: '10px', background: 'rgba(0, 0, 0, 0.8)', color: 'white', padding: '10px', borderRadius: '5px', fontSize: '12px', fontFamily: 'monospace', zIndex: 9999, maxWidth: '300px', maxHeight: '200px', overflow: 'auto', }} > <div style={{ fontWeight: 'bold', marginBottom: '5px' ;}}> 🚀 Web Vitals Debug </div> { metrics.length === 0 ? ( <div>等待性能指标...</div> ) : ( metrics.map((metric) => ( <div key={ metric.name} style={{ marginBottom: '3px' ;}}> <span style={{ color: metric.rating === 'good' ? '#4ade80' : metric.rating === 'needs-improvement' ? '#fbbf24' : '#f87171', }} > { metric.name} </span> : { Math.round(metric.value)} { metric.name === 'CLS' ? '' : 'ms'} ({ metric.rating}) </div> )) )} </div> ); } /* * Web Vitals Provider 组件
 */

export function WebVitalsProvider({ children, enableInDevelopment = true, onMetric, debug = false, }: WebVitalsProviderProps): React.JSX.Element { const metricsRef = useRef<WebVitalMetric[]>([]);

const collectorRef = useRef<ReturnType<typeof initWebVitals> | null>(null); useEffect(() => { // 检查是否应该启用性能监控 const shouldEnable = process.env.NODE_ENV === 'production' || (process.env.NODE_ENV === 'development' && enableInDevelopment); if (!shouldEnable) { return; } // 初始化Web Vitals收集器 const collector = initWebVitals(); collectorRef.current = collector; // 添加性能指标回调 collector.onMetric((metric) => { // 更新本地指标列表 metricsRef.current = [ ...metricsRef.current.filter((m) => m.name !== metric.name), metric, ]; // 调用自定义处理函数 if (onMetric !== undefined) { onMetric(metric); } // 开发环境调试输出 if (process.env.NODE_ENV === 'development' && debug) { // eslint-disable-next-line no-console console.group(`📊 Web Vital: ${ metric.name;}`); if (process.env.NODE_ENV === 'development') { if (process.env.NODE_ENV === 'development') { console.log('Value:', metric.value); } } if (process.env.NODE_ENV === 'development') { if (process.env.NODE_ENV === 'development') { console.log('Rating:', metric.rating); } } if (process.env.NODE_ENV === 'development') { if (process.env.NODE_ENV === 'development') { console.log('Delta:', metric.delta); } } if (process.env.NODE_ENV === 'development') { if (process.env.NODE_ENV === 'development') { console.log('ID:', metric.id); } } if (process.env.NODE_ENV === 'development') { if (process.env.NODE_ENV === 'development') { console.log('URL:', metric.url); } } // eslint-disable-next-line no-console console.groupEnd(); } }); // 页面卸载时导出性能报告 const handleBeforeUnload = (): void => { if (process.env.NODE_ENV === 'development' && debug) { const report = collector.exportReport(); if (process.env.NODE_ENV === 'development') { if (process.env.NODE_ENV === 'development') { console.log('📊 Web Vitals Report:', report); } } } 
};
 window.addEventListener('beforeunload', handleBeforeUnload); return () => { window.removeEventListener('beforeunload', handleBeforeUnload); 
};
 }, [enableInDevelopment, onMetric, debug]); return ( <> { children} { debug !== false&& process.env.NODE_ENV === 'development' && ( <PerformanceDebugPanel metrics={ metricsRef.current} /> )} </> ); }

export default WebVitalsProvider; 