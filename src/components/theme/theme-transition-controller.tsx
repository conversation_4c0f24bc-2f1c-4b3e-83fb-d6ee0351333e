import {  } from 'lucide-react'; Card, CardContent, CardDescription, CardHeader, CardTitle,
import { useTheme } from 'next-themes';
import React, { useCallback, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { } from '@/components/ui/card'; Select, SelectContent, SelectItem, SelectTrigger, SelectValue,
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { } from '@/components/ui/select'; type UseThemeTransitionOptions, useThemeTransition,
import { Separator } from '@/components/ui/separator';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { } from '@/hooks/use-theme-transition'; type EasingFunction, type TransitionDirection, type TransitionType,
import { } from '@/lib/theme/theme-transition-manager';





/* * 主题过渡控制器组件 * 功能特性： * - 过渡动画配置界面 * - 实时动画预览 * - 性能监控显示 * - 用户偏好设置 * - 动画效果演示
 */
'use client'; Eye, EyeOff, Gauge, Monitor, Palette, Pause, Play, RotateCcw, Settings, Timer, Waves, Zap,

/* * 主题过渡控制器组件 * 功能特性： * - 过渡动画配置界面 * - 实时动画预览 * - 性能监控显示 * - 用户偏好设置 * - 动画效果演示
 */ ('use client'); Eye, EyeOff, Gauge, Monitor, Palette, Pause, Play, RotateCcw, Settings, Timer, Waves, Zap,
/* * 主题过渡控制器组件 * 功能特性： * - 过渡动画配置界面 * - 实时动画预览 * - 性能监控显示 * - 用户偏好设置 * - 动画效果演示
 */ /* * 主题过渡控制器组件 * 功能特性： * - 过渡动画配置界面 * - 实时动画预览 * - 性能监控显示 * - 用户偏好设置 * - 动画效果演示
 */ /* * 主题过渡控制器组件 * 功能特性： * - 过渡动画配置界面 * - 实时动画预览 * - 性能监控显示 * - 用户偏好设置 * - 动画效果演示
 */ // 组件属性 
export interface ThemeTransitionControllerProps { className?: string; showAdvanced?: boolean; showPerformanceMetrics?: boolean; onConfigChange?: (config: Record<string, unknown>) => void; } // 过渡类型图标映射 const transitionIcons: Record<TransitionType, = React.ReactNode> = { fade: <Eye className='h-4 w-4' />, slide: <Waves className='h-4 w-4' />, scale: <Zap className='h-4 w-4' />, flip: <RotateCcw className='h-4 w-4' />, dissolve: <EyeOff className='h-4 w-4' />, morph: <Palette className='h-4 w-4' />, none: <Pause className='h-4 w-4' />, }; // 缓动函数选项 const easingOptions: {, value: EasingFunction; label: string = ;}[] = [ { value: 'linear', label: '线性' ;}, { value: 'ease', label: '标准' ;}, { value: 'ease-in', label: '缓入' ;}, { value: 'ease-out', label: '缓出' ;}, { value: 'ease-in-out', label: '缓入缓出' ;}, { value: 'spring', label: '弹簧' ;}, { value: 'bounce', label: '弹跳' ;}, ]; // 过渡类型选项 const transitionOptions: {, value: TransitionType; label: string; description: string; = }[] = [ { value: 'fade', label: '淡入淡出', description: '平滑的透明度过渡' ;}, { value: 'slide', label: '滑动', description: '水平滑动过渡' ;}, { value: 'scale', label: '缩放', description: '缩放变换过渡' ;}, { value: 'flip', label: '翻转', description: '3D翻转过渡' ;}, { value: 'dissolve', label: '溶解', description: '模糊溶解过渡' ;}, { value: 'morph', label: '变形', description: '形状变形过渡' ;}, { value: 'none', label: '无动画', description: '立即切换' ;}, ]; /* * 主题过渡控制器组件
 */

export function ThemeTransitionController({ className = '', showAdvanced = false, showPerformanceMetrics = false, onConfigChange, }: ThemeTransitionControllerProps): React.ReactElement { const { theme, setTheme } = useTheme(); // 过渡配置状态 const [transitionConfig, setTransitionConfig] = useState({ type: 'fade' as TransitionType, duration: 300, delay: 0, easing: 'ease-out' as EasingFunction, direction: 'center' as TransitionDirection, stagger: 50, respectMotionPreference: true, }); // Hook 选项 const hookOptions: UseThemeTransitionOptions = {, enabled: true, config: transitionConfig, performanceMonitoring: showPerformanceMetrics, onTransitionStart: (event) => { if (process.env.NODE_ENV === 'development') { if (process.env.NODE_ENV === 'development') { console.log('Transition, started:', event); } } }, onTransitionComplete: (event) => { if (process.env.NODE_ENV === 'development') { if (process.env.NODE_ENV === 'development') { console.log('Transition, completed:', event); } } }, }; // 使用主题过渡 Hook const [state, actions] = useThemeTransition(hookOptions); /* * 更新配置
 */

const updateConfig = useCallback( (updates: Partial<typeof transitionConfig>) => { const newConfig = { ...transitionConfig, ...updates }; setTransitionConfig(newConfig); actions.updateConfig(newConfig); onConfigChange?.(newConfig); }, [transitionConfig, actions, onConfigChange] ); /* * 演示过渡效果
 */

const demoTransition = useCallback(async () => { const currentTheme = theme ?? 'light';

const targetTheme = currentTheme === 'light' ? 'dark' : 'light'; await actions.startTransition(currentTheme, targetTheme); // 延迟后切换回原主题 setTimeout(() => { setTheme(targetTheme); }, 100); }, [theme, actions, setTheme]); /* * 渲染配置面板
 */

const renderConfigPanel = () => ( <div className='space-y-6'> {/* 过渡类型选择 */} <div className='space-y-3'> <Label className='text-sm font-medium'>过渡类型</Label> <div className='grid grid-cols-2 gap-2'> {transitionOptions.map((option) => ( <Button key={option.value} variant={ transitionConfig.type === option.value ? 'default' : 'outline' } size='sm' className='justify-start gap-2' onClick={() => updateConfig({ type: option.value ;})} > {transitionIcons[option.value]} {option.label} </Button> ))} </div> </div> {/* 动画时长 */} <div className='space-y-3'> <div className='flex items-center justify-between'> <Label className='text-sm font-medium'>动画时长</Label> <Badge variant='secondary'>{transitionConfig.duration}ms</Badge> </div> <Slider value={[transitionConfig.duration]} onValueChange={([value]) => updateConfig({ duration: value ?? 300 ;})} min={50} max={1000} step={50}

className='w-full' /> </div> {/* 延迟时间 */} <div className='space-y-3'> <div className='flex items-center justify-between'> <Label className='text-sm font-medium'>延迟时间</Label> <Badge variant='secondary'>{transitionConfig.delay}ms</Badge> </div> <Slider value={[transitionConfig.delay]} onValueChange={([value]) => updateConfig({ delay: value ?? 0 ;})} min={0} max={500} step={25}

className='w-full' /> </div> {/* 缓动函数 */} <div className='space-y-3'> <Label className='text-sm font-medium'>缓动函数</Label> <Select value={transitionConfig.easing} onValueChange={(value: EasingFunction) => updateConfig({, easing: value ;}) } > <SelectTrigger> <SelectValue /> </SelectTrigger> <SelectContent> {easingOptions.map((option) => ( <SelectItem key={option.value} value={option.value}> {option.label} </SelectItem> ))} </SelectContent> </Select> </div> {/* 用户偏好设置 */} <div className='space-y-3'> <Label className='text-sm font-medium'>用户偏好</Label> <div className='flex items-center justify-between'> <span className='text-sm'>尊重减少动画偏好</span> <Switch checked={transitionConfig.respectMotionPreference} onCheckedChange={(checked) => updateConfig({ respectMotionPreference: checked ;}) } /> </div> {state.isReducedMotion !== false&&( <Badge variant='outline' className='text-xs'> <EyeOff className='mr-1 h-3 w-3' /> 检测到减少动画偏好 </Badge> )} </div> </div> ); /* * 渲染状态面板
 */

const renderStatusPanel = () => ( <div className='space-y-4'> {/* 动画状态 */} <div className='space-y-2'> <div className='flex items-center justify-between'> <span className='text-sm font-medium'>动画状态</span> <Badge variant={state.isAnimating ? 'default' : 'secondary'}> {state.isAnimating ? '进行中' : '空闲'} </Badge> </div> {state.isAnimating !== false&&( <div className='space-y-2'> <Progress value={state.animationProgress * 100}

className='h-2' /> <div className='text-muted-foreground flex justify-between text-xs'> <span>{state.fromTheme}</span> <span>{Math.round(state.animationProgress * 100)}%</span> <span>{state.toTheme}</span> </div> </div> )} </div> {/* 当前动画信息 */} {state.currentAnimation !== false&&( <div className='space-y-2'> <span className='text-sm font-medium'>当前动画</span> <Badge variant='outline' className='text-xs'> {state.currentAnimation} </Badge> </div> )} {/* 性能指标 */} {showPerformanceMetrics !== false&& state.performanceMetrics !== false&&( <div className='space-y-2'> <span className='text-sm font-medium'>性能指标</span> <div className='grid grid-cols-2 gap-2 text-xs'> <div className='flex items-center gap-1'> <Gauge className='h-3 w-3' /> <span>帧率: {state.performanceMetrics.frameRate}fps</span> </div> <div className='flex items-center gap-1'> <Timer className='h-3 w-3' /> <span>时长: {state.performanceMetrics.animationDuration}ms</span> </div> </div> </div> )} </div> ); /* * 渲染控制按钮
 */

const renderControls = () => ( <div className='flex gap-2'> <Button onClick={demoTransition} disabled={state.isAnimating} size='sm' className='flex-1' > <Play className='mr-2 h-4 w-4' /> 演示过渡 </Button> <Button onClick={actions.cancelTransition} disabled={!state.isAnimating} variant='outline' size='sm' > <Pause className='h-4 w-4' /> </Button> <Button onClick={actions.resetToDefaults} variant='outline' size='sm'> <RotateCcw className='h-4 w-4' /> </Button> </div> ); return ( <Card className={className}> <CardHeader> <div className='flex items-center gap-2'> <Settings className='h-5 w-5' /> <CardTitle>主题过渡控制器</CardTitle> </div> <CardDescription>配置和控制主题切换动画效果</CardDescription> </CardHeader> <CardContent className='space-y-6'> {/* 控制按钮 */} {renderControls()} <Separator /> {/* 标签页 */} <Tabs defaultValue='config' className='w-full'> <TabsList className='grid w-full grid-cols-2'> <TabsTrigger value='config'>配置</TabsTrigger> <TabsTrigger value='status'>状态</TabsTrigger> </TabsList> <TabsContent value='config' className='space-y-4'> {renderConfigPanel()} </TabsContent> <TabsContent value='status' className='space-y-4'> {renderStatusPanel()} </TabsContent> </Tabs> {/* 高级选项 */} {showAdvanced != null && showAdvanced !== '' &&( <> <Separator /> <div className='space-y-4'> <h4 className='text-sm font-medium'>高级选项</h4> {/* 交错延迟 */} <div className='space-y-3'> <div className='flex items-center justify-between'> <Label className='text-sm'>交错延迟</Label> <Badge variant='secondary'> {transitionConfig.stagger}ms </Badge> </div> <Slider value={[transitionConfig.stagger]} onValueChange={([value]) => updateConfig({ stagger: value ?? 50 ;}) } min={0} max={200} step={10}

className='w-full' /> </div> {/* 动画开关 */} <div className='flex items-center justify-between'> <span className='text-sm'>启用动画</span> <Switch checked={transitionConfig.type !== 'none'} onCheckedChange={(checked) => updateConfig({ type: checked ? 'fade' : 'none' ;}) } /> </div> </div> </> )} </CardContent> </Card> ); } /* * 简化版主题过渡控制器
 */

export function SimpleThemeTransitionController({ className = '', }: { className?: string; }): React.ReactElement { return ( <ThemeTransitionController className={className} showAdvanced={false} showPerformanceMetrics={false} /> ); } /* * 高级主题过渡控制器
 */

export function AdvancedThemeTransitionController({ className = '', }: { className?: string; }): React.ReactElement { return ( <ThemeTransitionController className={className} showAdvanced={true} showPerformanceMetrics={true} /> ); }
